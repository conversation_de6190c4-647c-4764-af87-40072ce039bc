package com.card.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.card.domain.Member;
import com.card.domain.Wallet;
import com.card.domain.WalletTransaction;
import com.card.repository.WalletRepository;
import com.card.repository.WalletTransactionRepository;
import com.card.security.MemberSecurityUtils;
import com.card.service.dto.WalletTransactionDTO;
import com.card.service.mapper.WalletTransactionMapper;
import com.card.web.rest.errors.BadRequestAlertException;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;

/**
 * Unit tests for {@link WalletTransactionService}.
 */
@ExtendWith(MockitoExtension.class)
class WalletTransactionServiceTest {

    @Mock
    private WalletTransactionRepository walletTransactionRepository;

    @Mock
    private WalletTransactionMapper walletTransactionMapper;

    @Mock
    private WalletRepository walletRepository;

    @InjectMocks
    private WalletTransactionService walletTransactionService;

    private WalletTransaction sourceTransaction;
    private WalletTransaction relatedTransaction1;
    private WalletTransaction relatedTransaction2;
    private Member member;
    private Wallet wallet;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        member = new Member();
        member.setId(1L);
        member.setNickname("testuser");

        // 创建测试钱包
        wallet = new Wallet();
        wallet.setId(1L);
        wallet.setMember(member);
        wallet.setWalletAddress("0x1234567890abcdef");

        // 创建源交易
        sourceTransaction = new WalletTransaction();
        sourceTransaction.setId(1L);
        sourceTransaction.setUuid("uuid1");
        sourceTransaction.setTxHash("hash1");
        sourceTransaction.setTxType("SEND");
        sourceTransaction.setCoinType("ETH");
        sourceTransaction.setFromAddress("0x1234567890abcdef");
        sourceTransaction.setToAddress("0xabcdef1234567890");
        sourceTransaction.setAmount(new BigDecimal("1.0"));
        sourceTransaction.setStatus("SUCCESS");
        sourceTransaction.setCreateTime(Instant.now());
        sourceTransaction.setWallet(wallet);
        sourceTransaction.setMember(member);

        // 创建相关交易1 - 相同钱包和币种
        relatedTransaction1 = new WalletTransaction();
        relatedTransaction1.setId(2L);
        relatedTransaction1.setUuid("uuid2");
        relatedTransaction1.setTxHash("hash2");
        relatedTransaction1.setTxType("RECEIVE");
        relatedTransaction1.setCoinType("ETH");
        relatedTransaction1.setFromAddress("0xabcdef1234567890");
        relatedTransaction1.setToAddress("0x1234567890abcdef");
        relatedTransaction1.setAmount(new BigDecimal("0.5"));
        relatedTransaction1.setStatus("SUCCESS");
        relatedTransaction1.setCreateTime(Instant.now().minusSeconds(3600)); // 1小时前
        relatedTransaction1.setWallet(wallet);
        relatedTransaction1.setMember(member);

        // 创建相关交易2 - 相同地址
        relatedTransaction2 = new WalletTransaction();
        relatedTransaction2.setId(3L);
        relatedTransaction2.setUuid("uuid3");
        relatedTransaction2.setTxHash("hash3");
        relatedTransaction2.setTxType("SEND");
        relatedTransaction2.setCoinType("BTC");
        relatedTransaction2.setFromAddress("0x1234567890abcdef");
        relatedTransaction2.setToAddress("0x9876543210fedcba");
        relatedTransaction2.setAmount(new BigDecimal("0.1"));
        relatedTransaction2.setStatus("SUCCESS");
        relatedTransaction2.setCreateTime(Instant.now().minusSeconds(7200)); // 2小时前
        relatedTransaction2.setWallet(wallet);
        relatedTransaction2.setMember(member);
    }

    @Test
    void getRelatedTransactions_ShouldReturnRelatedTransactions_WhenValidInput() {
        // Given
        Long txId = 1L;
        Integer limit = 10;
        List<WalletTransaction> relatedTransactions = Arrays.asList(relatedTransaction1, relatedTransaction2);

        try (MockedStatic<MemberSecurityUtils> mockedSecurityUtils = mockStatic(MemberSecurityUtils.class)) {
            mockedSecurityUtils.when(MemberSecurityUtils::getCurrentMember).thenReturn(Optional.of(member));

            when(walletTransactionRepository.findById(txId)).thenReturn(Optional.of(sourceTransaction));
            when(walletTransactionRepository.findRelatedTransactions(
                eq(txId), anyString(), anyString(), anyString(), anyLong(), any(Instant.class), any(Instant.class), any(Pageable.class)
            )).thenReturn(relatedTransactions);

            WalletTransactionDTO sourceDto = new WalletTransactionDTO();
            sourceDto.setId(1L);
            WalletTransactionDTO relatedDto1 = new WalletTransactionDTO();
            relatedDto1.setId(2L);
            WalletTransactionDTO relatedDto2 = new WalletTransactionDTO();
            relatedDto2.setId(3L);

            when(walletTransactionMapper.toDto(sourceTransaction)).thenReturn(sourceDto);
            when(walletTransactionMapper.toDto(relatedTransaction1)).thenReturn(relatedDto1);
            when(walletTransactionMapper.toDto(relatedTransaction2)).thenReturn(relatedDto2);
            when(walletTransactionMapper.toEntity(relatedDto1)).thenReturn(relatedTransaction1);
            when(walletTransactionMapper.toEntity(relatedDto2)).thenReturn(relatedTransaction2);

            // When
            Map<String, Object> result = walletTransactionService.getRelatedTransactions(txId, limit);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.get("sourceTransaction")).isEqualTo(sourceDto);
            assertThat(result.get("totalFound")).isEqualTo(2);

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> scoredTransactions = (List<Map<String, Object>>) result.get("relatedTransactions");
            assertThat(scoredTransactions).hasSize(2);

            // 验证相关性分数计算
            Map<String, Object> firstScoredTx = scoredTransactions.get(0);
            assertThat(firstScoredTx.get("relevanceScore")).isInstanceOf(Integer.class);
            assertThat(firstScoredTx.get("relevanceTypes")).isInstanceOf(List.class);

            // 验证统计信息
            @SuppressWarnings("unchecked")
            Map<String, Object> stats = (Map<String, Object>) result.get("statistics");
            assertThat(stats).containsKeys("addressMatches", "coinTypeMatches", "timeRangeMatches", "walletMatches");
        }
    }

    @Test
    void getRelatedTransactions_ShouldThrowException_WhenTxIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> walletTransactionService.getRelatedTransactions(null, 10))
            .isInstanceOf(BadRequestAlertException.class)
            .hasMessageContaining("交易ID不能为空");
    }

    @Test
    void getRelatedTransactions_ShouldThrowException_WhenUserNotLoggedIn() {
        // Given
        try (MockedStatic<MemberSecurityUtils> mockedSecurityUtils = mockStatic(MemberSecurityUtils.class)) {
            mockedSecurityUtils.when(MemberSecurityUtils::getCurrentMember).thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> walletTransactionService.getRelatedTransactions(1L, 10))
                .isInstanceOf(BadRequestAlertException.class)
                .hasMessageContaining("用户未登录");
        }
    }

    @Test
    void getRelatedTransactions_ShouldThrowException_WhenTransactionNotFound() {
        // Given
        Long txId = 999L;

        try (MockedStatic<MemberSecurityUtils> mockedSecurityUtils = mockStatic(MemberSecurityUtils.class)) {
            mockedSecurityUtils.when(MemberSecurityUtils::getCurrentMember).thenReturn(Optional.of(member));
            when(walletTransactionRepository.findById(txId)).thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> walletTransactionService.getRelatedTransactions(txId, 10))
                .isInstanceOf(BadRequestAlertException.class)
                .hasMessageContaining("交易记录不存在");
        }
    }

    @Test
    void getRelatedTransactions_ShouldThrowException_WhenUserHasNoPermission() {
        // Given
        Long txId = 1L;
        Member otherMember = new Member();
        otherMember.setId(2L);

        try (MockedStatic<MemberSecurityUtils> mockedSecurityUtils = mockStatic(MemberSecurityUtils.class)) {
            mockedSecurityUtils.when(MemberSecurityUtils::getCurrentMember).thenReturn(Optional.of(otherMember));
            when(walletTransactionRepository.findById(txId)).thenReturn(Optional.of(sourceTransaction));

            // When & Then
            assertThatThrownBy(() -> walletTransactionService.getRelatedTransactions(txId, 10))
                .isInstanceOf(BadRequestAlertException.class)
                .hasMessageContaining("无权限查看此交易");
        }
    }

    @Test
    void getRelatedTransactions_ShouldUseDefaultLimit_WhenLimitIsNull() {
        // Given
        Long txId = 1L;
        List<WalletTransaction> relatedTransactions = Arrays.asList(relatedTransaction1);

        try (MockedStatic<MemberSecurityUtils> mockedSecurityUtils = mockStatic(MemberSecurityUtils.class)) {
            mockedSecurityUtils.when(MemberSecurityUtils::getCurrentMember).thenReturn(Optional.of(member));
            when(walletTransactionRepository.findById(txId)).thenReturn(Optional.of(sourceTransaction));
            when(walletTransactionRepository.findRelatedTransactions(
                eq(txId), anyString(), anyString(), anyString(), anyLong(), any(Instant.class), any(Instant.class), any(Pageable.class)
            )).thenReturn(relatedTransactions);

            WalletTransactionDTO sourceDto = new WalletTransactionDTO();
            sourceDto.setId(1L);
            WalletTransactionDTO relatedDto1 = new WalletTransactionDTO();
            relatedDto1.setId(2L);

            when(walletTransactionMapper.toDto(sourceTransaction)).thenReturn(sourceDto);
            when(walletTransactionMapper.toDto(relatedTransaction1)).thenReturn(relatedDto1);
            when(walletTransactionMapper.toEntity(relatedDto1)).thenReturn(relatedTransaction1);

            // When
            Map<String, Object> result = walletTransactionService.getRelatedTransactions(txId, null);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.get("totalFound")).isEqualTo(1);

            // 验证使用了默认的分页大小
            verify(walletTransactionRepository).findRelatedTransactions(
                eq(txId), anyString(), anyString(), anyString(), anyLong(), any(Instant.class), any(Instant.class), 
                argThat(pageable -> pageable.getPageSize() == 10)
            );
        }
    }

    @Test
    void getRelatedTransactions_ShouldCapLimit_WhenLimitExceedsMaximum() {
        // Given
        Long txId = 1L;
        Integer limit = 200; // 超过最大限制100
        List<WalletTransaction> relatedTransactions = Arrays.asList(relatedTransaction1);

        try (MockedStatic<MemberSecurityUtils> mockedSecurityUtils = mockStatic(MemberSecurityUtils.class)) {
            mockedSecurityUtils.when(MemberSecurityUtils::getCurrentMember).thenReturn(Optional.of(member));
            when(walletTransactionRepository.findById(txId)).thenReturn(Optional.of(sourceTransaction));
            when(walletTransactionRepository.findRelatedTransactions(
                eq(txId), anyString(), anyString(), anyString(), anyLong(), any(Instant.class), any(Instant.class), any(Pageable.class)
            )).thenReturn(relatedTransactions);

            WalletTransactionDTO sourceDto = new WalletTransactionDTO();
            sourceDto.setId(1L);
            WalletTransactionDTO relatedDto1 = new WalletTransactionDTO();
            relatedDto1.setId(2L);

            when(walletTransactionMapper.toDto(sourceTransaction)).thenReturn(sourceDto);
            when(walletTransactionMapper.toDto(relatedTransaction1)).thenReturn(relatedDto1);
            when(walletTransactionMapper.toEntity(relatedDto1)).thenReturn(relatedTransaction1);

            // When
            Map<String, Object> result = walletTransactionService.getRelatedTransactions(txId, limit);

            // Then
            assertThat(result).isNotNull();

            // 验证限制被设置为最大值100
            verify(walletTransactionRepository).findRelatedTransactions(
                eq(txId), anyString(), anyString(), anyString(), anyLong(), any(Instant.class), any(Instant.class), 
                argThat(pageable -> pageable.getPageSize() == 100)
            );
        }
    }
}
