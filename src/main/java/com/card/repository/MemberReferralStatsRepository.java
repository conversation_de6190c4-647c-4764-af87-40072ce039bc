package com.card.repository;

import com.card.domain.MemberReferralStats;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 会员推荐统计数据访问层
 * Spring Data JPA repository for the MemberReferralStats entity.
 */
@SuppressWarnings("unused")
@Repository
public interface MemberReferralStatsRepository extends JpaRepository<MemberReferralStats, Long> {
    /**
     * 根据会员ID和统计月份查找推荐统计记录
     * Find referral stats by member ID and stat month
     *
     * @param memberId 会员ID
     * @param statMonth 统计月份（格式：YYYY-MM）
     * @return 推荐统计记录
     */
    @Query("SELECT mrs FROM MemberReferralStats mrs WHERE mrs.member.id = :memberId AND mrs.statMonth = :statMonth AND mrs.deleted = false")
    Optional<MemberReferralStats> findByMemberIdAndStatMonth(@Param("memberId") Long memberId, @Param("statMonth") String statMonth);

    /**
     * 根据会员ID查找所有统计记录
     * Find all referral stats by member ID
     *
     * @param memberId 会员ID
     * @return 推荐统计记录列表
     */
    @Query("SELECT mrs FROM MemberReferralStats mrs WHERE mrs.member.id = :memberId AND mrs.deleted = false ORDER BY mrs.statMonth DESC")
    List<MemberReferralStats> findByMemberId(@Param("memberId") Long memberId);

    /**
     * 根据统计月份查找所有会员的统计记录
     * Find all referral stats by stat month
     *
     * @param statMonth 统计月份
     * @return 推荐统计记录列表
     */
    @Query(
        "SELECT mrs FROM MemberReferralStats mrs WHERE mrs.statMonth = :statMonth AND mrs.deleted = false ORDER BY mrs.totalReward DESC, mrs.directReferrals DESC"
    )
    List<MemberReferralStats> findByStatMonth(@Param("statMonth") String statMonth);

    /**
     * 查找指定时间范围内的统计记录
     * Find referral stats within time range
     *
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 推荐统计记录列表
     */
    @Query(
        "SELECT mrs FROM MemberReferralStats mrs WHERE mrs.statMonth BETWEEN :startMonth AND :endMonth AND mrs.deleted = false ORDER BY mrs.statMonth DESC, mrs.totalReward DESC"
    )
    List<MemberReferralStats> findByStatMonthBetween(@Param("startMonth") String startMonth, @Param("endMonth") String endMonth);

    /**
     * 根据直接推荐人数排序查找统计记录
     * Find referral stats ordered by direct referrals count
     *
     * @param statMonth 统计月份
     * @param minDirectReferrals 最小直接推荐人数
     * @return 推荐统计记录列表
     */
    @Query(
        "SELECT mrs FROM MemberReferralStats mrs WHERE mrs.statMonth = :statMonth AND mrs.directReferrals >= :minDirectReferrals AND mrs.deleted = false ORDER BY mrs.directReferrals DESC"
    )
    List<MemberReferralStats> findTopPerformersByDirectReferrals(
        @Param("statMonth") String statMonth,
        @Param("minDirectReferrals") Integer minDirectReferrals
    );

    /**
     * 根据总奖励金额排序查找统计记录
     * Find referral stats ordered by total reward amount
     *
     * @param statMonth 统计月份
     * @param minTotalReward 最小总奖励金额
     * @return 推荐统计记录列表
     */
    @Query(
        "SELECT mrs FROM MemberReferralStats mrs WHERE mrs.statMonth = :statMonth AND mrs.totalReward >= :minTotalReward AND mrs.deleted = false ORDER BY mrs.totalReward DESC"
    )
    List<MemberReferralStats> findTopPerformersByTotalReward(
        @Param("statMonth") String statMonth,
        @Param("minTotalReward") java.math.BigDecimal minTotalReward
    );

    /**
     * 统计指定月份的总推荐人数
     * Count total referrals in specific month
     *
     * @param statMonth 统计月份
     * @return 总推荐人数
     */
    @Query(
        "SELECT COALESCE(SUM(mrs.monthlyReferrals), 0) FROM MemberReferralStats mrs WHERE mrs.statMonth = :statMonth AND mrs.deleted = false"
    )
    Long sumMonthlyReferralsByStatMonth(@Param("statMonth") String statMonth);

    /**
     * 统计指定月份的总奖励金额
     * Sum total reward amount in specific month
     *
     * @param statMonth 统计月份
     * @return 总奖励金额
     */
    @Query(
        "SELECT COALESCE(SUM(mrs.monthlyReward), 0) FROM MemberReferralStats mrs WHERE mrs.statMonth = :statMonth AND mrs.deleted = false"
    )
    java.math.BigDecimal sumMonthlyRewardByStatMonth(@Param("statMonth") String statMonth);

    /**
     * 统计指定月份活跃推荐人数（本月有推荐的会员数）
     * Count active referrers in specific month
     *
     * @param statMonth 统计月份
     * @return 活跃推荐人数
     */
    @Query(
        "SELECT COUNT(DISTINCT mrs.member.id) FROM MemberReferralStats mrs WHERE mrs.statMonth = :statMonth AND mrs.monthlyReferrals > 0 AND mrs.deleted = false"
    )
    Long countActiveReferrersByStatMonth(@Param("statMonth") String statMonth);

    /**
     * 根据UUID查找推荐统计记录
     * Find referral stats by UUID
     *
     * @param uuid 业务唯一标识
     * @return 推荐统计记录
     */
    @Query("SELECT mrs FROM MemberReferralStats mrs WHERE mrs.uuid = :uuid AND mrs.deleted = false")
    Optional<MemberReferralStats> findByUuid(@Param("uuid") String uuid);

    /**
     * 批量更新统计记录的更新时间
     * Batch update stats records update time
     *
     * @param memberIds 会员ID列表
     * @param updateTime 更新时间
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE MemberReferralStats mrs SET mrs.updateTime = :updateTime WHERE mrs.member.id IN :memberIds AND mrs.deleted = false")
    int batchUpdateTimeByMemberIds(@Param("memberIds") List<Long> memberIds, @Param("updateTime") Instant updateTime);

    /**
     * 查找需要更新的统计记录（最近未更新的记录）
     * Find stats records that need update
     *
     * @param beforeTime 指定时间之前的记录
     * @return 需要更新的统计记录列表
     */
    @Query("SELECT mrs FROM MemberReferralStats mrs WHERE mrs.updateTime < :beforeTime AND mrs.deleted = false ORDER BY mrs.updateTime ASC")
    List<MemberReferralStats> findStatsNeedingUpdate(@Param("beforeTime") Instant beforeTime);

    /**
     * 统计会员在指定时间范围内的总推荐奖励
     * Sum member's total rewards within time range
     *
     * @param memberId 会员ID
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 总奖励金额
     */
    @Query(
        "SELECT COALESCE(SUM(mrs.totalReward), 0) FROM MemberReferralStats mrs WHERE mrs.member.id = :memberId AND mrs.statMonth BETWEEN :startMonth AND :endMonth AND mrs.deleted = false"
    )
    java.math.BigDecimal sumTotalRewardByMemberIdAndPeriod(
        @Param("memberId") Long memberId,
        @Param("startMonth") String startMonth,
        @Param("endMonth") String endMonth
    );
}
