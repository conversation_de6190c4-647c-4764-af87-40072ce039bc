package com.card.repository;

import com.card.domain.MemberRelations;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 会员关系数据访问层
 * Spring Data JPA repository for the MemberRelations entity.
 */
@SuppressWarnings("unused")
@Repository
public interface MemberRelationsRepository extends JpaRepository<MemberRelations, Long> {
    /**
     * 根据会员ID和关系状态查找推荐关系
     * Find member relation by member ID and status
     *
     * @param memberId 会员ID
     * @param status 关系状态（1-有效，0-失效，2-暂停，3-冻结）
     * @return 推荐关系记录
     */
    @Query("SELECT mr FROM MemberRelations mr WHERE mr.member.id = :memberId AND mr.status = :status AND mr.deleted = false")
    Optional<MemberRelations> findByMemberIdAndStatus(@Param("memberId") Long memberId, @Param("status") Integer status);

    /**
     * 根据会员ID查找所有推荐关系（不考虑状态）
     * Find all member relations by member ID
     *
     * @param memberId 会员ID
     * @return 推荐关系记录列表
     */
    @Query("SELECT mr FROM MemberRelations mr WHERE mr.member.id = :memberId AND mr.deleted = false ORDER BY mr.createTime DESC")
    List<MemberRelations> findByMemberId(@Param("memberId") Long memberId);

    /**
     * 根据上级会员ID查找所有下级推荐关系
     * Find all subordinate member relations by parent member ID
     *
     * @param parentId 上级会员ID
     * @return 下级推荐关系记录列表
     */
    @Query("SELECT mr FROM MemberRelations mr WHERE mr.parent.id = :parentId AND mr.deleted = false ORDER BY mr.createTime DESC")
    List<MemberRelations> findByParentId(@Param("parentId") Long parentId);

    /**
     * 根据上级会员ID和关系状态查找下级推荐关系
     * Find subordinate member relations by parent ID and status
     *
     * @param parentId 上级会员ID
     * @param status 关系状态
     * @return 下级推荐关系记录列表
     */
    @Query(
        "SELECT mr FROM MemberRelations mr WHERE mr.parent.id = :parentId AND mr.status = :status AND mr.deleted = false ORDER BY mr.createTime DESC"
    )
    List<MemberRelations> findByParentIdAndStatus(@Param("parentId") Long parentId, @Param("status") Integer status);

    /**
     * 根据推荐码查找推荐关系
     * Find member relations by referral code
     *
     * @param referralCode 推荐码
     * @return 推荐关系记录列表
     */
    @Query("SELECT mr FROM MemberRelations mr WHERE mr.referralCode = :referralCode AND mr.deleted = false ORDER BY mr.createTime DESC")
    List<MemberRelations> findByReferralCode(@Param("referralCode") String referralCode);

    /**
     * 根据关系路径查找推荐关系（用于层级查询）
     * Find member relations by relation path pattern
     *
     * @param pathPattern 路径模式（支持LIKE查询）
     * @return 推荐关系记录列表
     */
    @Query(
        "SELECT mr FROM MemberRelations mr WHERE mr.relationPath LIKE :pathPattern AND mr.deleted = false ORDER BY mr.level ASC, mr.createTime DESC"
    )
    List<MemberRelations> findByRelationPathContaining(@Param("pathPattern") String pathPattern);

    /**
     * 统计会员的直接推荐人数
     * Count direct referrals by member ID
     *
     * @param parentId 推荐人ID
     * @param status 关系状态
     * @return 直接推荐人数
     */
    @Query("SELECT COUNT(mr) FROM MemberRelations mr WHERE mr.parent.id = :parentId AND mr.status = :status AND mr.deleted = false")
    Long countDirectReferralsByParentIdAndStatus(@Param("parentId") Long parentId, @Param("status") Integer status);

    /**
     * 根据层级范围查找推荐关系
     * Find member relations by level range
     *
     * @param minLevel 最小层级
     * @param maxLevel 最大层级
     * @return 推荐关系记录列表
     */
    @Query(
        "SELECT mr FROM MemberRelations mr WHERE mr.level BETWEEN :minLevel AND :maxLevel AND mr.deleted = false ORDER BY mr.level ASC, mr.createTime DESC"
    )
    List<MemberRelations> findByLevelBetween(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 根据UUID查找推荐关系
     * Find member relation by UUID
     *
     * @param uuid 业务唯一标识
     * @return 推荐关系记录
     */
    @Query("SELECT mr FROM MemberRelations mr WHERE mr.uuid = :uuid AND mr.deleted = false")
    Optional<MemberRelations> findByUuid(@Param("uuid") String uuid);

    /**
     * 批量更新会员关系状态
     * Batch update member relations status
     *
     * @param memberIds 会员ID列表
     * @param status 新状态
     * @param updateTime 更新时间
     * @return 更新的记录数
     */
    @Modifying
    @Query(
        "UPDATE MemberRelations mr SET mr.status = :status, mr.updateTime = :updateTime WHERE mr.member.id IN :memberIds AND mr.deleted = false"
    )
    int batchUpdateStatusByMemberIds(
        @Param("memberIds") List<Long> memberIds,
        @Param("status") Integer status,
        @Param("updateTime") Instant updateTime
    );

    /**
     * 查找会员的完整推荐链路（从根节点到当前会员）
     * Find complete referral chain for a member
     *
     * @param memberId 会员ID
     * @return 推荐链路记录列表（按层级排序）
     */
    @Query(
        value = """
        WITH RECURSIVE referral_chain AS (
            -- 起始节点：查找指定会员的推荐关系
            SELECT mr.id, mr.uuid, mr.member_id, mr.parent_id, mr.referral_code,
                   mr.relation_type, mr.level, mr.relation_path, mr.status,
                   mr.effective_time, mr.create_time, 1 as chain_level
            FROM member_relations mr
            WHERE mr.member_id = :memberId AND mr.deleted = false

            UNION ALL

            -- 递归部分：查找上级的推荐关系
            SELECT mr.id, mr.uuid, mr.member_id, mr.parent_id, mr.referral_code,
                   mr.relation_type, mr.level, mr.relation_path, mr.status,
                   mr.effective_time, mr.create_time, rc.chain_level + 1
            FROM member_relations mr
            INNER JOIN referral_chain rc ON mr.member_id = rc.parent_id
            WHERE mr.deleted = false
        )
        SELECT * FROM referral_chain ORDER BY chain_level DESC
        """,
        nativeQuery = true
    )
    List<MemberRelations> findReferralChainByMemberId(@Param("memberId") Long memberId);

    /**
     * 查找会员的所有下级推荐树（包括多层级）
     * Find complete referral tree for a member (all subordinates)
     *
     * @param parentId 推荐人ID
     * @return 推荐树记录列表（按层级和创建时间排序）
     */
    @Query(
        value = """
        WITH RECURSIVE referral_tree AS (
            -- 起始节点：查找直接下级
            SELECT mr.id, mr.uuid, mr.member_id, mr.parent_id, mr.referral_code,
                   mr.relation_type, mr.level, mr.relation_path, mr.status,
                   mr.effective_time, mr.create_time, 1 as tree_level
            FROM member_relations mr
            WHERE mr.parent_id = :parentId AND mr.deleted = false

            UNION ALL

            -- 递归部分：查找下级的下级
            SELECT mr.id, mr.uuid, mr.member_id, mr.parent_id, mr.referral_code,
                   mr.relation_type, mr.level, mr.relation_path, mr.status,
                   mr.effective_time, mr.create_time, rt.tree_level + 1
            FROM member_relations mr
            INNER JOIN referral_tree rt ON mr.parent_id = rt.member_id
            WHERE mr.deleted = false
        )
        SELECT * FROM referral_tree ORDER BY tree_level ASC, create_time ASC
        """,
        nativeQuery = true
    )
    List<MemberRelations> findReferralTreeByParentId(@Param("parentId") Long parentId);

    /**
     * 检查是否存在循环推荐关系
     * Check if circular referral relationship exists
     *
     * @param memberId 会员ID
     * @param targetParentId 目标推荐人ID
     * @return 如果存在循环关系返回true
     */
    @Query(
        value = """
        WITH RECURSIVE check_cycle AS (
            -- 起始节点：从目标推荐人开始
            SELECT mr.member_id, mr.parent_id, 1 as depth
            FROM member_relations mr
            WHERE mr.member_id = :targetParentId AND mr.deleted = false

            UNION ALL

            -- 递归检查上级链路
            SELECT mr.member_id, mr.parent_id, cc.depth + 1
            FROM member_relations mr
            INNER JOIN check_cycle cc ON mr.member_id = cc.parent_id
            WHERE mr.deleted = false AND cc.depth < 50  -- 防止无限递归
        )
        SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
        FROM check_cycle
        WHERE member_id = :memberId OR parent_id = :memberId
        """,
        nativeQuery = true
    )
    Integer checkCircularReferral(@Param("memberId") Long memberId, @Param("targetParentId") Long targetParentId);
}
