package com.card.repository;

import com.card.domain.WalletTransaction;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the WalletTransaction entity.
 */
@SuppressWarnings("unused")
@Repository
public interface WalletTransactionRepository extends JpaRepository<WalletTransaction, Long> {
    /**
     * 根据钱包ID查询最新的交易记录
     * @param walletId 钱包ID
     * @param pageable 分页参数
     * @return 最新交易记录
     */
    @Query("SELECT wt FROM WalletTransaction wt WHERE wt.wallet.id = :walletId ORDER BY wt.createTime DESC")
    Page<WalletTransaction> findLatestTransactionsByWalletId(@Param("walletId") Long walletId, Pageable pageable);

    /**
     * 根据用户ID查询最新的交易记录
     * @param memberId 用户ID
     * @param pageable 分页参数
     * @return 最新交易记录
     */
    @Query("SELECT wt FROM WalletTransaction wt WHERE wt.member.id = :memberId ORDER BY wt.createTime DESC")
    Page<WalletTransaction> findLatestTransactionsByMemberId(@Param("memberId") Long memberId, Pageable pageable);

    /**
     * 根据钱包ID和时间范围查询最新交易
     * @param walletId 钱包ID
     * @param afterTime 时间起点
     * @param pageable 分页参数
     * @return 最新交易记录
     */
    @Query("SELECT wt FROM WalletTransaction wt WHERE wt.wallet.id = :walletId AND wt.createTime > :afterTime ORDER BY wt.createTime DESC")
    Page<WalletTransaction> findLatestTransactionsByWalletIdAfterTime(
        @Param("walletId") Long walletId,
        @Param("afterTime") Instant afterTime,
        Pageable pageable
    );

    /**
     * 根据用户ID和时间范围查询最新交易
     * @param memberId 用户ID
     * @param afterTime 时间起点
     * @param pageable 分页参数
     * @return 最新交易记录
     */
    @Query("SELECT wt FROM WalletTransaction wt WHERE wt.member.id = :memberId AND wt.createTime > :afterTime ORDER BY wt.createTime DESC")
    Page<WalletTransaction> findLatestTransactionsByMemberIdAfterTime(
        @Param("memberId") Long memberId,
        @Param("afterTime") Instant afterTime,
        Pageable pageable
    );

    /**
     * 查询指定钱包的最新一条交易
     * @param walletId 钱包ID
     * @return 最新交易记录
     */
    @Query("SELECT wt FROM WalletTransaction wt WHERE wt.wallet.id = :walletId ORDER BY wt.createTime DESC")
    List<WalletTransaction> findTopTransactionByWalletId(@Param("walletId") Long walletId, Pageable pageable);

    /**
     * 根据钱包ID和多个过滤条件查询交易记录
     * @param walletId 钱包ID
     * @param txType 交易类型
     * @param coinType 币种类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 交易记录
     */
    @Query(
        "SELECT wt FROM WalletTransaction wt WHERE wt.wallet.id = :walletId " +
        "AND (:txType IS NULL OR wt.txType = :txType) " +
        "AND (:coinType IS NULL OR wt.coinType = :coinType) " +
        "AND (:startTime IS NULL OR wt.createTime >= :startTime) " +
        "AND (:endTime IS NULL OR wt.createTime <= :endTime) " +
        "ORDER BY wt.createTime DESC"
    )
    Page<WalletTransaction> findByWalletIdAndFilters(
        @Param("walletId") Long walletId,
        @Param("txType") String txType,
        @Param("coinType") String coinType,
        @Param("startTime") Instant startTime,
        @Param("endTime") Instant endTime,
        Pageable pageable
    );

    /**
     * 根据用户ID和多个过滤条件查询交易记录
     * @param memberId 用户ID
     * @param txType 交易类型
     * @param coinType 币种类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 交易记录
     */
    @Query(
        "SELECT wt FROM WalletTransaction wt WHERE wt.member.id = :memberId " +
        "AND (:txType IS NULL OR wt.txType = :txType) " +
        "AND (:coinType IS NULL OR wt.coinType = :coinType) " +
        "AND (:startTime IS NULL OR wt.createTime >= :startTime) " +
        "AND (:endTime IS NULL OR wt.createTime <= :endTime) " +
        "ORDER BY wt.createTime DESC"
    )
    Page<WalletTransaction> findByMemberIdAndFilters(
        @Param("memberId") Long memberId,
        @Param("txType") String txType,
        @Param("coinType") String coinType,
        @Param("startTime") Instant startTime,
        @Param("endTime") Instant endTime,
        Pageable pageable
    );

    /**
     * 查找与指定交易相关的其他交易记录
     * 根据地址匹配、币种匹配、钱包匹配和时间范围查找相关交易
     * @param excludeId 要排除的交易ID（原始交易）
     * @param fromAddress 发送方地址
     * @param toAddress 接收方地址
     * @param coinType 币种类型
     * @param walletId 钱包ID
     * @param startTime 时间范围开始
     * @param endTime 时间范围结束
     * @param pageable 分页参数
     * @return 相关交易记录列表
     */
    @Query(
        "SELECT wt FROM WalletTransaction wt WHERE wt.id != :excludeId " +
        "AND (" +
        "    wt.wallet.id = :walletId " +
        "    OR (:coinType IS NOT NULL AND wt.coinType = :coinType) " +
        "    OR (:fromAddress IS NOT NULL AND (wt.fromAddress = :fromAddress OR wt.toAddress = :fromAddress)) " +
        "    OR (:toAddress IS NOT NULL AND (wt.fromAddress = :toAddress OR wt.toAddress = :toAddress)) " +
        ") " +
        "AND (:startTime IS NULL OR wt.createTime >= :startTime) " +
        "AND (:endTime IS NULL OR wt.createTime <= :endTime) " +
        "ORDER BY wt.createTime DESC"
    )
    List<WalletTransaction> findRelatedTransactions(
        @Param("excludeId") Long excludeId,
        @Param("fromAddress") String fromAddress,
        @Param("toAddress") String toAddress,
        @Param("coinType") String coinType,
        @Param("walletId") Long walletId,
        @Param("startTime") Instant startTime,
        @Param("endTime") Instant endTime,
        Pageable pageable
    );
}
