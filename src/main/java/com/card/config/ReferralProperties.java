package com.card.config;

import org.springframework.stereotype.Component;

/**
 * 推荐关系配置属性
 * Configuration properties for referral system
 */
@Component
public class ReferralProperties {

    private final ApplicationProperties applicationProperties;

    public ReferralProperties(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    /**
     * 获取推荐关系最大层级深度限制
     */
    public Integer getMaxLevel() {
        return applicationProperties.getReferral().getMaxLevel();
    }

    /**
     * 获取是否启用推荐功能
     */
    public Boolean getEnabled() {
        return applicationProperties.getReferral().getEnabled();
    }

    /**
     * 获取推荐奖励是否启用
     */
    public Boolean getRewardEnabled() {
        return applicationProperties.getReferral().getRewardEnabled();
    }
}
