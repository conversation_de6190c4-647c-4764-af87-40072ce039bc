package com.card.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件上传配置类
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadProperties {

    /**
     * 上传提供商：tencent 或 aliyun
     */
    private String provider = "tencent";

    /**
     * 腾讯云COS配置
     */
    private TencentCOS tencent = new TencentCOS();

    /**
     * 阿里云OSS配置
     */
    private AliyunOSS aliyun = new AliyunOSS();

    /**
     * 腾讯云COS配置
     */
    @Getter
    @Setter
    public static class TencentCOS {

        /**
         * 密钥ID
         */
        private String secretId;

        /**
         * 密钥Key
         */
        private String secretKey;

        /**
         * 地域
         */
        private String region;

        /**
         * 存储桶名称
         */
        private String bucketName;

        /**
         * 访问域名
         */
        private String domain;

        /**
         * 文件存储路径前缀
         */
        private String pathPrefix = "avatars";
    }

    /**
     * 阿里云OSS配置
     */
    @Getter
    @Setter
    public static class AliyunOSS {

        /**
         * 访问密钥ID
         */
        private String accessKeyId;

        /**
         * 访问密钥Secret
         */
        private String accessKeySecret;

        /**
         * 节点
         */
        private String endpoint;

        /**
         * 存储桶名称
         */
        private String bucketName;

        /**
         * 访问域名
         */
        private String domain;

        /**
         * 文件存储路径前缀
         */
        private String pathPrefix = "avatars";
    }
}
