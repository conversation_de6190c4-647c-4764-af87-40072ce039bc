package com.card.service;

import com.card.domain.Member;
import com.card.domain.Wallet;
import com.card.domain.WalletTransaction;
import com.card.repository.WalletRepository;
import com.card.repository.WalletTransactionRepository;
import com.card.security.MemberSecurityUtils;
import com.card.service.dto.WalletTransactionDTO;
import com.card.service.mapper.WalletTransactionMapper;
import com.card.web.rest.errors.BadRequestAlertException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.card.domain.WalletTransaction}.
 */
@Service
@Transactional
public class WalletTransactionService {

    private static final Logger LOG = LoggerFactory.getLogger(WalletTransactionService.class);

    private final WalletTransactionRepository walletTransactionRepository;

    private final WalletTransactionMapper walletTransactionMapper;

    private final WalletRepository walletRepository;

    public WalletTransactionService(
        WalletTransactionRepository walletTransactionRepository,
        WalletTransactionMapper walletTransactionMapper,
        WalletRepository walletRepository
    ) {
        this.walletTransactionRepository = walletTransactionRepository;
        this.walletTransactionMapper = walletTransactionMapper;
        this.walletRepository = walletRepository;
    }

    /**
     * Save a walletTransaction.
     *
     * @param walletTransactionDTO the entity to save.
     * @return the persisted entity.
     */
    public WalletTransactionDTO save(WalletTransactionDTO walletTransactionDTO) {
        LOG.debug("Request to save WalletTransaction : {}", walletTransactionDTO);
        WalletTransaction walletTransaction = walletTransactionMapper.toEntity(walletTransactionDTO);
        walletTransaction = walletTransactionRepository.save(walletTransaction);
        return walletTransactionMapper.toDto(walletTransaction);
    }

    /**
     * Update a walletTransaction.
     *
     * @param walletTransactionDTO the entity to save.
     * @return the persisted entity.
     */
    public WalletTransactionDTO update(WalletTransactionDTO walletTransactionDTO) {
        LOG.debug("Request to update WalletTransaction : {}", walletTransactionDTO);
        WalletTransaction walletTransaction = walletTransactionMapper.toEntity(walletTransactionDTO);
        walletTransaction = walletTransactionRepository.save(walletTransaction);
        return walletTransactionMapper.toDto(walletTransaction);
    }

    /**
     * Partially update a walletTransaction.
     *
     * @param walletTransactionDTO the entity to update partially.
     * @return the persisted entity.
     */
    public Optional<WalletTransactionDTO> partialUpdate(WalletTransactionDTO walletTransactionDTO) {
        LOG.debug("Request to partially update WalletTransaction : {}", walletTransactionDTO);

        return walletTransactionRepository
            .findById(walletTransactionDTO.getId())
            .map(existingWalletTransaction -> {
                walletTransactionMapper.partialUpdate(existingWalletTransaction, walletTransactionDTO);

                return existingWalletTransaction;
            })
            .map(walletTransactionRepository::save)
            .map(walletTransactionMapper::toDto);
    }

    /**
     * Get all the walletTransactions.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    @Transactional(readOnly = true)
    public Page<WalletTransactionDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all WalletTransactions");
        return walletTransactionRepository.findAll(pageable).map(walletTransactionMapper::toDto);
    }

    /**
     * Get one walletTransaction by id.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    @Transactional(readOnly = true)
    public Optional<WalletTransactionDTO> findOne(Long id) {
        LOG.debug("Request to get WalletTransaction : {}", id);
        return walletTransactionRepository.findById(id).map(walletTransactionMapper::toDto);
    }

    /**
     * Delete the walletTransaction by id.
     *
     * @param id the id of the entity.
     */
    public void delete(Long id) {
        LOG.debug("Request to delete WalletTransaction : {}", id);
        walletTransactionRepository.deleteById(id);
    }

    // ==================== 业务方法 ====================

    /**
     * 发起转账
     */
    public Map<String, Object> transfer(Map<String, Object> transferData) {
        LOG.debug("Request to transfer : {}", transferData);

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        // 验证钱包所有权
        Long walletId = Long.valueOf(transferData.get("walletId").toString());
        Wallet wallet = walletRepository
            .findById(walletId)
            .orElseThrow(() -> new BadRequestAlertException("钱包不存在", "WalletTransaction", "walletnotfound"));

        if (!wallet.getMember().getId().equals(currentMemberId)) {
            throw new BadRequestAlertException("无权限操作此钱包", "WalletTransaction", "nopermission");
        }

        // 创建并保存交易记录
        WalletTransaction transaction = createTransaction(
            wallet,
            transferData.get("amount").toString(),
            transferData.get("coinType").toString(),
            transferData.get("toAddress").toString(),
            transferData.get("memo"),
            transferData.get("gasPrice"),
            transferData.get("gasLimit")
        );

        Map<String, Object> result = new HashMap<>();
        result.put("transactionId", transaction.getId());
        result.put("status", "SUCCESS");
        result.put("message", "转账请求已提交");

        return result;
    }

    /**
     * 批量转账
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> batchTransfer(Map<String, Object> batchTransferData) {
        LOG.debug("Request to batch transfer : {}", batchTransferData);

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        // 验证钱包所有权
        Long walletId = Long.valueOf(batchTransferData.get("fromWalletId").toString());
        Wallet wallet = walletRepository
            .findById(walletId)
            .orElseThrow(() -> new BadRequestAlertException("钱包不存在", "WalletTransaction", "walletnotfound"));

        if (!wallet.getMember().getId().equals(currentMemberId)) {
            throw new BadRequestAlertException("无权限操作此钱包", "WalletTransaction", "nopermission");
        }

        // 获取收款人列表
        List<Map<String, Object>> recipients;
        try {
            recipients = (List<Map<String, Object>>) batchTransferData.get("recipients");
            if (recipients == null || recipients.isEmpty()) {
                throw new BadRequestAlertException("收款人列表不能为空", "WalletTransaction", "recipientsEmpty");
            }
        } catch (ClassCastException e) {
            throw new BadRequestAlertException("收款人列表格式错误", "WalletTransaction", "recipientsFormatError");
        }

        // 批量创建交易记录
        String coinType = batchTransferData.get("coinType").toString();
        Object gasPrice = batchTransferData.get("gasPrice");
        Object gasLimit = batchTransferData.get("gasLimit");

        List<WalletTransaction> transactions = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (Map<String, Object> recipient : recipients) {
            String toAddress = recipient.get("toAddress").toString();
            String amount = recipient.get("amount").toString();
            Object memo = recipient.get("memo");

            // 累计总金额
            totalAmount = totalAmount.add(new BigDecimal(amount));

            // 创建交易
            WalletTransaction transaction = createTransaction(wallet, amount, coinType, toAddress, memo, gasPrice, gasLimit);

            transactions.add(transaction);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("transactionIds", transactions.stream().map(WalletTransaction::getId).collect(Collectors.toList()));
        result.put("totalAmount", totalAmount);
        result.put("totalTransactions", transactions.size());
        result.put("status", "SUCCESS");
        result.put("message", "批量转账请求已提交");

        return result;
    }

    /**
     * 创建单笔交易记录
     *
     * @param wallet 钱包
     * @param amount 金额
     * @param coinType 币种类型
     * @param toAddress 接收方地址
     * @param memo 备注
     * @param gasPrice Gas价格
     * @param gasLimit Gas限制
     * @return 保存后的交易记录
     */
    private WalletTransaction createTransaction(
        Wallet wallet,
        String amount,
        String coinType,
        String toAddress,
        Object memo,
        Object gasPrice,
        Object gasLimit
    ) {
        // 创建交易记录
        WalletTransaction transaction = new WalletTransaction();
        transaction.setWallet(wallet);
        transaction.setTxType("SEND");
        transaction.setAmount(new BigDecimal(amount));
        transaction.setCoinType(coinType);
        transaction.setToAddress(toAddress);
        transaction.setFromAddress(wallet.getWalletAddress());
        transaction.setStatus("PENDING");
        transaction.setCreateTime(Instant.now());
        transaction.setUpdateTime(Instant.now());

        // 安全地设置Member
        MemberSecurityUtils.getCurrentMember().ifPresent(transaction::setMember);

        // 安全地设置memo字段
        if (memo != null) {
            transaction.setMemo(memo.toString());
        }

        // 安全地设置gasPrice字段
        if (gasPrice != null) {
            try {
                transaction.setGasPrice(new BigDecimal(gasPrice.toString()));
            } catch (NumberFormatException e) {
                LOG.warn("Invalid gasPrice format: {}", gasPrice);
            }
        }

        // 安全地设置gasLimit字段
        if (gasLimit != null) {
            try {
                transaction.setGasLimit(Long.valueOf(gasLimit.toString()));
            } catch (NumberFormatException e) {
                LOG.warn("Invalid gasLimit format: {}", gasLimit);
            }
        }

        // 设置uuid - 生成一个唯一标识符
        transaction.setUuid(java.util.UUID.randomUUID().toString().replace("-", ""));

        // 设置txHash - 在实际区块链交易提交之前，可以设置一个临时哈希
        // 在实际应用中，这个值应该从区块链交易提交后获取
        String tempTxHash = "tx_" + java.util.UUID.randomUUID().toString().replace("-", "");
        transaction.setTxHash(tempTxHash);

        // 保存交易记录
        return walletTransactionRepository.save(transaction);
    }

    /**
     * 生成收款二维码
     */
    public Map<String, Object> generatePaymentQR(Map<String, Object> paymentData) {
        LOG.debug("Request to generate payment QR : {}", paymentData);

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        Map<String, Object> result = new HashMap<>();
        result.put(
            "qrCode",
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        );
        result.put("paymentUrl", "https://example.com/payment/" + System.currentTimeMillis());
        result.put("expiryTime", Instant.now().plusSeconds(1800)); // 30分钟后过期

        return result;
    }

    /**
     * 获取交易历史
     */
    public Map<String, Object> getTransactionHistory(
        Long walletId,
        String txType,
        String coinType,
        String startTime,
        String endTime,
        Integer pageNum,
        Integer pageSize
    ) {
        LOG.debug(
            "Request to get transaction history : walletId={}, txType={}, coinType={}, startTime={}, endTime={}, pageNum={}, pageSize={}",
            walletId,
            txType,
            coinType,
            startTime,
            endTime,
            pageNum,
            pageSize
        );

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        // 如果指定了钱包ID，验证钱包所有权
        if (walletId != null) {
            Wallet wallet = walletRepository
                .findById(walletId)
                .orElseThrow(() -> new BadRequestAlertException("钱包不存在", "WalletTransaction", "walletnotfound"));

            if (!wallet.getMember().getId().equals(currentMemberId)) {
                throw new BadRequestAlertException("无权限查看此钱包交易记录", "WalletTransaction", "nopermission");
            }
        }

        // 创建分页对象
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize, Sort.by(Sort.Direction.DESC, "createTime"));

        // 解析时间参数
        Instant startInstant = null;
        Instant endInstant = null;
        if (startTime != null && !startTime.isEmpty()) {
            try {
                startInstant = Instant.parse(startTime);
            } catch (Exception e) {
                LOG.warn("Invalid startTime format: {}", startTime);
            }
        }
        if (endTime != null && !endTime.isEmpty()) {
            try {
                endInstant = Instant.parse(endTime);
            } catch (Exception e) {
                LOG.warn("Invalid endTime format: {}", endTime);
            }
        }

        // 查询交易记录
        Page<WalletTransaction> transactions;

        if (walletId != null) {
            // 查询指定钱包的交易记录
            transactions = walletTransactionRepository.findByWalletIdAndFilters(
                walletId,
                txType,
                coinType,
                startInstant,
                endInstant,
                pageable
            );
        } else {
            // 查询当前用户所有钱包的交易记录
            transactions = walletTransactionRepository.findByMemberIdAndFilters(
                currentMemberId,
                txType,
                coinType,
                startInstant,
                endInstant,
                pageable
            );
        }

        // 转换为DTO并返回结果
        List<WalletTransactionDTO> transactionDTOs = transactions
            .getContent()
            .stream()
            .map(walletTransactionMapper::toDto)
            .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("transactions", transactionDTOs);
        result.put("totalElements", transactions.getTotalElements());
        result.put("totalPages", transactions.getTotalPages());
        result.put("currentPage", pageNum);
        result.put("pageSize", pageSize);

        return result;
    }

    /**
     * 获取交易详情
     */
    public Map<String, Object> getTransactionDetail(Long txId) {
        LOG.debug("Request to get transaction detail : {}", txId);

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        // 查询交易记录
        WalletTransaction transaction = walletTransactionRepository
            .findById(txId)
            .orElseThrow(() -> new BadRequestAlertException("交易记录不存在", "WalletTransaction", "transactionnotfound"));

        // 验证交易所有权
        if (!transaction.getWallet().getMember().getId().equals(currentMemberId)) {
            throw new BadRequestAlertException("无权限查看此交易", "WalletTransaction", "nopermission");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("transaction", transaction);

        return result;
    }

    /**
     * 获取交易统计
     */
    public Map<String, Object> getTransactionStats(Long walletId, String period) {
        LOG.debug("Request to get transaction stats : walletId={}, period={}", walletId, period);

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        // 如果指定了钱包ID，验证钱包所有权
        if (walletId != null) {
            Wallet wallet = walletRepository
                .findById(walletId)
                .orElseThrow(() -> new BadRequestAlertException("钱包不存在", "WalletTransaction", "walletnotfound"));

            if (!wallet.getMember().getId().equals(currentMemberId)) {
                throw new BadRequestAlertException("无权限查看此钱包统计", "WalletTransaction", "nopermission");
            }
        }

        // 计算时间范围
        Instant endTime = Instant.now();
        Instant startTime = calculateStartTime(period, endTime);

        // 查询交易记录
        List<WalletTransaction> transactions;
        if (walletId != null) {
            // 查询指定钱包的交易记录
            transactions = walletTransactionRepository
                .findByWalletIdAndFilters(walletId, null, null, startTime, endTime, Pageable.unpaged())
                .getContent();
        } else {
            // 查询当前用户所有钱包的交易记录
            transactions = walletTransactionRepository
                .findByMemberIdAndFilters(currentMemberId, null, null, startTime, endTime, Pageable.unpaged())
                .getContent();
        }

        // 计算统计数据
        Map<String, Object> stats = calculateStats(transactions);

        // 添加时间范围信息
        stats.put("period", period);
        stats.put("startTime", startTime);
        stats.put("endTime", endTime);

        return stats;
    }

    /**
     * 根据周期计算开始时间
     */
    private Instant calculateStartTime(String period, Instant endTime) {
        if (period == null || period.isEmpty()) {
            period = "month"; // 默认为一个月
        }

        LocalDateTime endDateTime = LocalDateTime.ofInstant(endTime, ZoneId.systemDefault());
        LocalDateTime startDateTime;

        switch (period.toLowerCase()) {
            case "day":
            case "today":
                startDateTime = endDateTime.toLocalDate().atStartOfDay();
                break;
            case "week":
                startDateTime = endDateTime.minusWeeks(1);
                break;
            case "month":
                startDateTime = endDateTime.minusMonths(1);
                break;
            case "quarter":
                startDateTime = endDateTime.minusMonths(3);
                break;
            case "year":
                startDateTime = endDateTime.minusYears(1);
                break;
            case "all":
                startDateTime = LocalDateTime.of(2020, 1, 1, 0, 0); // 设置一个很早的时间
                break;
            default:
                startDateTime = endDateTime.minusMonths(1); // 默认一个月
                break;
        }

        return startDateTime.atZone(ZoneId.systemDefault()).toInstant();
    }

    /**
     * 计算交易统计数据
     */
    private Map<String, Object> calculateStats(List<WalletTransaction> transactions) {
        Map<String, Object> stats = new HashMap<>();

        if (transactions.isEmpty()) {
            stats.put("totalTransactions", 0L);
            stats.put("totalAmount", BigDecimal.ZERO);
            stats.put("totalSendAmount", BigDecimal.ZERO);
            stats.put("totalReceiveAmount", BigDecimal.ZERO);
            stats.put("totalGasFee", BigDecimal.ZERO);
            stats.put("successRate", "0%");
            stats.put("pendingRate", "0%");
            stats.put("failedRate", "0%");
            stats.put("sendCount", 0L);
            stats.put("receiveCount", 0L);
            stats.put("approveCount", 0L);
            stats.put("coinTypeStats", Map.of());
            stats.put("dailyStats", List.of());
            return stats;
        }

        // 基础统计
        long totalTransactions = transactions.size();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalSendAmount = BigDecimal.ZERO;
        BigDecimal totalReceiveAmount = BigDecimal.ZERO;
        BigDecimal totalGasFee = BigDecimal.ZERO;

        // 状态统计
        long successCount = 0;
        long pendingCount = 0;
        long failedCount = 0;

        // 交易类型统计
        long sendCount = 0;
        long receiveCount = 0;
        long approveCount = 0;

        // 币种统计
        Map<String, Map<String, Object>> coinTypeStats = new HashMap<>();

        // 日期统计（按天分组）
        Map<String, BigDecimal> dailyAmountMap = new HashMap<>();
        Map<String, Long> dailyCountMap = new HashMap<>();

        DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (WalletTransaction tx : transactions) {
            // 基础金额统计
            BigDecimal amount = tx.getAmount() != null ? tx.getAmount() : BigDecimal.ZERO;
            totalAmount = totalAmount.add(amount);

            // Gas费统计
            if (tx.getGasFee() != null) {
                totalGasFee = totalGasFee.add(tx.getGasFee());
            }

            // 按交易类型统计
            String txType = tx.getTxType();
            if ("SEND".equals(txType)) {
                sendCount++;
                totalSendAmount = totalSendAmount.add(amount);
            } else if ("RECEIVE".equals(txType)) {
                receiveCount++;
                totalReceiveAmount = totalReceiveAmount.add(amount);
            } else if ("APPROVE".equals(txType)) {
                approveCount++;
            }

            // 状态统计
            String status = tx.getStatus();
            if ("SUCCESS".equals(status) || "COMPLETED".equals(status)) {
                successCount++;
            } else if ("PENDING".equals(status)) {
                pendingCount++;
            } else if ("FAILED".equals(status) || "CANCELLED".equals(status)) {
                failedCount++;
            }

            // 币种统计
            String coinType = tx.getCoinType();
            if (coinType != null) {
                coinTypeStats.computeIfAbsent(coinType, k -> {
                    Map<String, Object> coinStats = new HashMap<>();
                    coinStats.put("count", 0L);
                    coinStats.put("amount", BigDecimal.ZERO);
                    coinStats.put("sendCount", 0L);
                    coinStats.put("receiveCount", 0L);
                    return coinStats;
                });

                Map<String, Object> coinStats = coinTypeStats.get(coinType);
                coinStats.put("count", (Long) coinStats.get("count") + 1);
                coinStats.put("amount", ((BigDecimal) coinStats.get("amount")).add(amount));

                if ("SEND".equals(txType)) {
                    coinStats.put("sendCount", (Long) coinStats.get("sendCount") + 1);
                } else if ("RECEIVE".equals(txType)) {
                    coinStats.put("receiveCount", (Long) coinStats.get("receiveCount") + 1);
                }
            }

            // 日期统计
            if (tx.getCreateTime() != null) {
                String dayKey = tx.getCreateTime().atZone(ZoneId.systemDefault()).format(dayFormatter);
                dailyAmountMap.put(dayKey, dailyAmountMap.getOrDefault(dayKey, BigDecimal.ZERO).add(amount));
                dailyCountMap.put(dayKey, dailyCountMap.getOrDefault(dayKey, 0L) + 1);
            }
        }

        // 计算百分比
        String successRate = String.format("%.1f%%", ((double) successCount / totalTransactions) * 100);
        String pendingRate = String.format("%.1f%%", ((double) pendingCount / totalTransactions) * 100);
        String failedRate = String.format("%.1f%%", ((double) failedCount / totalTransactions) * 100);

        // 构建日期统计列表
        List<Map<String, Object>> dailyStats = dailyAmountMap
            .entrySet()
            .stream()
            .map(entry -> {
                Map<String, Object> dailyStat = new HashMap<>();
                dailyStat.put("date", entry.getKey());
                dailyStat.put("amount", entry.getValue());
                dailyStat.put("count", dailyCountMap.get(entry.getKey()));
                return dailyStat;
            })
            .sorted((a, b) -> ((String) a.get("date")).compareTo((String) b.get("date")))
            .collect(Collectors.toList());

        // 组装结果
        stats.put("totalTransactions", totalTransactions);
        stats.put("totalAmount", totalAmount);
        stats.put("totalSendAmount", totalSendAmount);
        stats.put("totalReceiveAmount", totalReceiveAmount);
        stats.put("totalGasFee", totalGasFee);
        stats.put("successRate", successRate);
        stats.put("pendingRate", pendingRate);
        stats.put("failedRate", failedRate);
        stats.put("successCount", successCount);
        stats.put("pendingCount", pendingCount);
        stats.put("failedCount", failedCount);
        stats.put("sendCount", sendCount);
        stats.put("receiveCount", receiveCount);
        stats.put("approveCount", approveCount);
        stats.put("coinTypeStats", coinTypeStats);
        stats.put("dailyStats", dailyStats);

        return stats;
    }

    /**
     * 获取相关交易
     * 查找与指定交易相关的其他交易记录，包括：
     * 1. 相同地址的交易（发送方或接收方）
     * 2. 相同币种的交易
     * 3. 相近时间的交易（前后24小时）
     * 4. 相同钱包的交易
     *
     * @param txId 交易ID
     * @param limit 返回结果数量限制
     * @return 相关交易列表及统计信息
     */
    public Map<String, Object> getRelatedTransactions(Long txId, Integer limit) {
        LOG.debug("Request to get related transactions : txId={}, limit={}", txId, limit);

        // 参数验证
        if (txId == null) {
            throw new BadRequestAlertException("交易ID不能为空", "WalletTransaction", "txIdNull");
        }

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        // 查询原始交易记录
        WalletTransaction sourceTransaction = walletTransactionRepository
            .findById(txId)
            .orElseThrow(() -> new BadRequestAlertException("交易记录不存在", "WalletTransaction", "transactionnotfound"));

        // 验证交易所有权
        if (sourceTransaction.getWallet() == null || sourceTransaction.getWallet().getMember() == null) {
            throw new BadRequestAlertException("交易记录数据异常", "WalletTransaction", "transactionDataError");
        }

        if (!sourceTransaction.getWallet().getMember().getId().equals(currentMemberId)) {
            throw new BadRequestAlertException("无权限查看此交易", "WalletTransaction", "nopermission");
        }

        // 设置查询参数
        String fromAddress = sourceTransaction.getFromAddress();
        String toAddress = sourceTransaction.getToAddress();
        String coinType = sourceTransaction.getCoinType();
        Long walletId = sourceTransaction.getWallet().getId();
        Instant transactionTime = sourceTransaction.getCreateTime();

        // 设置时间范围（交易时间前后24小时）
        Instant startTime = transactionTime != null ? transactionTime.minusSeconds(86400) : null; // 24小时前
        Instant endTime = transactionTime != null ? transactionTime.plusSeconds(86400) : null;    // 24小时后

        // 限制返回结果数量
        if (limit == null || limit <= 0) {
            limit = 10; // 默认限制为10条
        }

        // 确保limit不超过最大值
        if (limit > 100) {
            limit = 100; // 最大限制为100条
        }

        // 分页设置
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createTime"));

        try {
            // 查询相关交易
            List<WalletTransaction> relatedTransactions = walletTransactionRepository.findRelatedTransactions(
                txId,                // 排除原交易
                fromAddress,         // 相同发送方地址
                toAddress,           // 相同接收方地址
                coinType,            // 相同币种
                walletId,            // 相同钱包
                startTime,           // 时间范围开始
                endTime,             // 时间范围结束
                pageable             // 分页和排序
            );

            LOG.debug("Found {} related transactions for txId: {}", relatedTransactions.size(), txId);

            // 转换为DTO
            List<WalletTransactionDTO> relatedTransactionDTOs = relatedTransactions.stream()
                .map(walletTransactionMapper::toDto)
                .collect(Collectors.toList());

            // 计算相关性分数
            List<Map<String, Object>> scoredTransactions = new ArrayList<>();
            for (WalletTransactionDTO tx : relatedTransactionDTOs) {
                try {
                    Map<String, Object> scoredTx = new HashMap<>();
                    scoredTx.put("transaction", tx);

                    // 计算相关性分数 (0-100)
                    int score = calculateRelevanceScore(sourceTransaction, walletTransactionMapper.toEntity(tx));
                    scoredTx.put("relevanceScore", score);

                    // 确定相关性类型
                    List<String> relevanceTypes = determineRelevanceTypes(sourceTransaction, walletTransactionMapper.toEntity(tx));
                    scoredTx.put("relevanceTypes", relevanceTypes);

                    scoredTransactions.add(scoredTx);
                } catch (Exception e) {
                    LOG.warn("Error processing transaction {} for relevance scoring: {}", tx.getId(), e.getMessage());
                    // 继续处理其他交易，不中断整个流程
                }
            }

            // 按相关性分数排序
            scoredTransactions.sort((a, b) ->
                Integer.compare((Integer)b.get("relevanceScore"), (Integer)a.get("relevanceScore")));

            // 构建结果
            Map<String, Object> result = new HashMap<>();
            result.put("sourceTransaction", walletTransactionMapper.toDto(sourceTransaction));
            result.put("relatedTransactions", scoredTransactions);
            result.put("totalFound", relatedTransactions.size());

            // 添加统计信息
            Map<String, Object> stats = new HashMap<>();
            stats.put("addressMatches", countAddressMatches(sourceTransaction, relatedTransactions));
            stats.put("coinTypeMatches", countCoinTypeMatches(sourceTransaction, relatedTransactions));
            stats.put("timeRangeMatches", countTimeRangeMatches(sourceTransaction, relatedTransactions, startTime, endTime));
            stats.put("walletMatches", countWalletMatches(sourceTransaction, relatedTransactions));
            result.put("statistics", stats);

            return result;

        } catch (Exception e) {
            LOG.error("Error getting related transactions for txId: {}", txId, e);
            throw new BadRequestAlertException("查询相关交易失败", "WalletTransaction", "queryRelatedTransactionsFailed");
        }
    }
    
    /**
     * 计算两个交易之间的相关性分数 (0-100)
     */
    private int calculateRelevanceScore(WalletTransaction source, WalletTransaction target) {
        if (source == null || target == null) {
            return 0;
        }

        int score = 0;

        // 相同钱包 +30分
        if (source.getWallet() != null && target.getWallet() != null &&
            source.getWallet().getId() != null && target.getWallet().getId() != null &&
            source.getWallet().getId().equals(target.getWallet().getId())) {
            score += 30;
        }

        // 相同币种 +20分
        if (source.getCoinType() != null && target.getCoinType() != null &&
            source.getCoinType().equals(target.getCoinType())) {
            score += 20;
        }

        // 地址匹配
        // 发送方地址匹配 +15分
        if (source.getFromAddress() != null && target.getFromAddress() != null &&
            source.getFromAddress().equals(target.getFromAddress())) {
            score += 15;
        }

        // 接收方地址匹配 +15分
        if (source.getToAddress() != null && target.getToAddress() != null &&
            source.getToAddress().equals(target.getToAddress())) {
            score += 15;
        }

        // 交叉地址匹配（源交易的发送方是目标交易的接收方，或反之） +10分
        if ((source.getFromAddress() != null && target.getToAddress() != null &&
             source.getFromAddress().equals(target.getToAddress())) ||
            (source.getToAddress() != null && target.getFromAddress() != null &&
             source.getToAddress().equals(target.getFromAddress()))) {
            score += 10;
        }

        // 时间接近度 (最多 +20分)
        if (source.getCreateTime() != null && target.getCreateTime() != null) {
            long timeDiffSeconds = Math.abs(source.getCreateTime().getEpochSecond() - target.getCreateTime().getEpochSecond());

            // 1小时内 +20分
            if (timeDiffSeconds < 3600) {
                score += 20;
            }
            // 6小时内 +15分
            else if (timeDiffSeconds < 21600) {
                score += 15;
            }
            // 12小时内 +10分
            else if (timeDiffSeconds < 43200) {
                score += 10;
            }
            // 24小时内 +5分
            else if (timeDiffSeconds < 86400) {
                score += 5;
            }
        }

        return Math.min(score, 100); // 确保分数不超过100
    }
    
    /**
     * 确定两个交易之间的相关性类型
     */
    private List<String> determineRelevanceTypes(WalletTransaction source, WalletTransaction target) {
        List<String> types = new ArrayList<>();

        if (source == null || target == null) {
            return types;
        }

        // 相同钱包
        if (source.getWallet() != null && target.getWallet() != null &&
            source.getWallet().getId() != null && target.getWallet().getId() != null &&
            source.getWallet().getId().equals(target.getWallet().getId())) {
            types.add("SAME_WALLET");
        }

        // 相同币种
        if (source.getCoinType() != null && target.getCoinType() != null &&
            source.getCoinType().equals(target.getCoinType())) {
            types.add("SAME_COIN");
        }

        // 发送方地址匹配
        if (source.getFromAddress() != null && target.getFromAddress() != null &&
            source.getFromAddress().equals(target.getFromAddress())) {
            types.add("SAME_SENDER");
        }

        // 接收方地址匹配
        if (source.getToAddress() != null && target.getToAddress() != null &&
            source.getToAddress().equals(target.getToAddress())) {
            types.add("SAME_RECEIVER");
        }

        // 交叉地址匹配
        if (source.getFromAddress() != null && target.getToAddress() != null &&
            source.getFromAddress().equals(target.getToAddress())) {
            types.add("CROSS_ADDRESS_SENDER_TO_RECEIVER");
        }

        if (source.getToAddress() != null && target.getFromAddress() != null &&
            source.getToAddress().equals(target.getFromAddress())) {
            types.add("CROSS_ADDRESS_RECEIVER_TO_SENDER");
        }

        // 时间接近度
        if (source.getCreateTime() != null && target.getCreateTime() != null) {
            long timeDiffSeconds = Math.abs(source.getCreateTime().getEpochSecond() - target.getCreateTime().getEpochSecond());

            if (timeDiffSeconds < 3600) {
                types.add("TIME_WITHIN_1H");
            } else if (timeDiffSeconds < 21600) {
                types.add("TIME_WITHIN_6H");
            } else if (timeDiffSeconds < 43200) {
                types.add("TIME_WITHIN_12H");
            } else if (timeDiffSeconds < 86400) {
                types.add("TIME_WITHIN_24H");
            }
        }

        return types;
    }
    
    /**
     * 统计地址匹配的交易数量
     */
    private Map<String, Integer> countAddressMatches(WalletTransaction source, List<WalletTransaction> transactions) {
        Map<String, Integer> counts = new HashMap<>();

        if (source == null || transactions == null) {
            counts.put("senderMatches", 0);
            counts.put("receiverMatches", 0);
            counts.put("crossMatches", 0);
            counts.put("totalAddressMatches", 0);
            return counts;
        }

        int senderMatches = 0;
        int receiverMatches = 0;
        int crossMatches = 0;

        for (WalletTransaction tx : transactions) {
            if (tx == null) continue;

            // 发送方地址匹配
            if (source.getFromAddress() != null && tx.getFromAddress() != null &&
                source.getFromAddress().equals(tx.getFromAddress())) {
                senderMatches++;
            }

            // 接收方地址匹配
            if (source.getToAddress() != null && tx.getToAddress() != null &&
                source.getToAddress().equals(tx.getToAddress())) {
                receiverMatches++;
            }

            // 交叉地址匹配
            if ((source.getFromAddress() != null && tx.getToAddress() != null &&
                 source.getFromAddress().equals(tx.getToAddress())) ||
                (source.getToAddress() != null && tx.getFromAddress() != null &&
                 source.getToAddress().equals(tx.getFromAddress()))) {
                crossMatches++;
            }
        }

        counts.put("senderMatches", senderMatches);
        counts.put("receiverMatches", receiverMatches);
        counts.put("crossMatches", crossMatches);
        counts.put("totalAddressMatches", senderMatches + receiverMatches + crossMatches);

        return counts;
    }
    
    /**
     * 统计币种匹配的交易数量
     */
    private Map<String, Integer> countCoinTypeMatches(WalletTransaction source, List<WalletTransaction> transactions) {
        Map<String, Integer> counts = new HashMap<>();

        if (source == null || transactions == null) {
            counts.put("coinTypeMatches", 0);
            return counts;
        }

        int coinTypeMatches = 0;

        for (WalletTransaction tx : transactions) {
            if (tx != null && source.getCoinType() != null && tx.getCoinType() != null &&
                source.getCoinType().equals(tx.getCoinType())) {
                coinTypeMatches++;
            }
        }

        counts.put("coinTypeMatches", coinTypeMatches);

        return counts;
    }

    /**
     * 统计时间范围内的交易数量
     */
    private Map<String, Integer> countTimeRangeMatches(
        WalletTransaction source,
        List<WalletTransaction> transactions,
        Instant startTime,
        Instant endTime
    ) {
        Map<String, Integer> counts = new HashMap<>();

        int within1h = 0;
        int within6h = 0;
        int within12h = 0;
        int within24h = 0;

        if (source == null || transactions == null || source.getCreateTime() == null) {
            counts.put("within1h", within1h);
            counts.put("within6h", within6h);
            counts.put("within12h", within12h);
            counts.put("within24h", within24h);
            return counts;
        }

        for (WalletTransaction tx : transactions) {
            if (tx != null && tx.getCreateTime() != null) {
                long timeDiffSeconds = Math.abs(source.getCreateTime().getEpochSecond() - tx.getCreateTime().getEpochSecond());

                if (timeDiffSeconds < 3600) {
                    within1h++;
                }

                if (timeDiffSeconds < 21600) {
                    within6h++;
                }

                if (timeDiffSeconds < 43200) {
                    within12h++;
                }

                if (timeDiffSeconds < 86400) {
                    within24h++;
                }
            }
        }

        counts.put("within1h", within1h);
        counts.put("within6h", within6h);
        counts.put("within12h", within12h);
        counts.put("within24h", within24h);

        return counts;
    }

    /**
     * 统计钱包匹配的交易数量
     */
    private Map<String, Integer> countWalletMatches(WalletTransaction source, List<WalletTransaction> transactions) {
        Map<String, Integer> counts = new HashMap<>();

        if (source == null || transactions == null || source.getWallet() == null || source.getWallet().getId() == null) {
            counts.put("walletMatches", 0);
            return counts;
        }

        int walletMatches = 0;

        for (WalletTransaction tx : transactions) {
            if (tx != null && tx.getWallet() != null && tx.getWallet().getId() != null &&
                source.getWallet().getId().equals(tx.getWallet().getId())) {
                walletMatches++;
            }
        }

        counts.put("walletMatches", walletMatches);

        return counts;
    }

    /**
     * 取消交易
     */
    public Map<String, Object> cancelTransaction(Long txId, String password) {
        LOG.debug("Request to cancel transaction : txId={}", txId);

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        // 查询交易记录
        WalletTransaction transaction = walletTransactionRepository
            .findById(txId)
            .orElseThrow(() -> new BadRequestAlertException("交易记录不存在", "WalletTransaction", "transactionnotfound"));

        // 验证交易所有权
        if (!transaction.getWallet().getMember().getId().equals(currentMemberId)) {
            throw new BadRequestAlertException("无权限操作此交易", "WalletTransaction", "nopermission");
        }

        // 更新交易状态
        transaction.setStatus("CANCELLED");
        transaction.setUpdateTime(Instant.now());
        walletTransactionRepository.save(transaction);

        Map<String, Object> result = new HashMap<>();
        result.put("status", "SUCCESS");
        result.put("message", "交易已取消");

        return result;
    }

    /**
     * 加速交易
     */
    public Map<String, Object> speedUpTransaction(Long txId, String newGasPrice, String password) {
        LOG.debug("Request to speed up transaction : txId={}, newGasPrice={}", txId, newGasPrice);

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        // 查询交易记录
        WalletTransaction transaction = walletTransactionRepository
            .findById(txId)
            .orElseThrow(() -> new BadRequestAlertException("交易记录不存在", "WalletTransaction", "transactionnotfound"));

        // 验证交易所有权
        if (!transaction.getWallet().getMember().getId().equals(currentMemberId)) {
            throw new BadRequestAlertException("无权限操作此交易", "WalletTransaction", "nopermission");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("status", "SUCCESS");
        result.put("message", "交易加速成功");

        return result;
    }

    /**
     * 估算Gas费用
     */
    public Map<String, Object> estimateGas(Map<String, Object> gasData) {
        LOG.debug("Request to estimate gas : {}", gasData);

        Map<String, Object> result = new HashMap<>();
        result.put("gasPrice", "20000000000"); // 20 Gwei
        result.put("gasLimit", "21000");
        result.put("estimatedFee", "0.00042");

        return result;
    }

    /**
     * 导出交易记录
     */
    public Map<String, Object> exportTransactions(Map<String, Object> exportData) {
        LOG.debug("Request to export transactions : {}", exportData);

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        Map<String, Object> result = new HashMap<>();
        result.put("downloadUrl", "https://example.com/download/transactions_" + System.currentTimeMillis() + ".csv");
        result.put("expiryTime", Instant.now().plusSeconds(3600)); // 1小时后过期

        return result;
    }

    /**
     * 获取Gas费估算
     */
    public Map<String, Object> estimateGasFee(String fromAddress, String toAddress, String amount, String coinType) {
        LOG.debug(
            "Request to estimate gas fee : fromAddress={}, toAddress={}, amount={}, coinType={}",
            fromAddress,
            toAddress,
            amount,
            coinType
        );

        Map<String, Object> result = new HashMap<>();
        result.put("slow", Map.of("gasPrice", "10000000000", "estimatedTime", "10-30分钟", "fee", "0.00021"));
        result.put("standard", Map.of("gasPrice", "20000000000", "estimatedTime", "3-5分钟", "fee", "0.00042"));
        result.put("fast", Map.of("gasPrice", "30000000000", "estimatedTime", "1-2分钟", "fee", "0.00063"));

        return result;
    }

    /**
     * 验证交易密码
     */
    public Map<String, Object> verifyTransactionPassword(String password) {
        LOG.debug("Request to verify transaction password");

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        Map<String, Object> result = new HashMap<>();
        result.put("valid", true);
        result.put("message", "密码验证成功");

        return result;
    }

    /**
     * 获取收款记录
     */
    public Map<String, Object> getPaymentRequests(Long walletId, String status, Integer pageNum, Integer pageSize) {
        LOG.debug("Request to get payment requests : walletId={}, status={}, pageNum={}, pageSize={}", walletId, status, pageNum, pageSize);

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        Map<String, Object> result = new HashMap<>();
        result.put("paymentRequests", List.of());
        result.put("totalElements", 0L);
        result.put("totalPages", 0);
        result.put("currentPage", pageNum);
        result.put("pageSize", pageSize);

        return result;
    }

    /**
     * 搜索交易
     */
    public Map<String, Object> searchTransactions(String keyword, Long walletId, Integer pageNum, Integer pageSize) {
        LOG.debug(
            "Request to search transactions : keyword={}, walletId={}, pageNum={}, pageSize={}",
            keyword,
            walletId,
            pageNum,
            pageSize
        );

        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        Map<String, Object> result = new HashMap<>();
        result.put("transactions", List.of());
        result.put("totalElements", 0L);
        result.put("totalPages", 0);
        result.put("currentPage", pageNum);
        result.put("pageSize", pageSize);

        return result;
    }

    /**
     * 获取钱包最新交易记录
     * @param walletId 钱包ID
     * @param limit 限制数量
     * @return 最新交易记录列表
     */
    @Transactional(readOnly = true)
    public List<WalletTransactionDTO> getLatestTransactionsByWallet(Long walletId, int limit) {
        LOG.debug("Request to get latest transactions for wallet: {}, limit: {}", walletId, limit);

        Pageable pageable = PageRequest.of(0, limit);
        Page<WalletTransaction> transactions = walletTransactionRepository.findLatestTransactionsByWalletId(walletId, pageable);

        return transactions.getContent().stream().map(walletTransactionMapper::toDto).toList();
    }

    /**
     * 获取用户最新交易记录
     * @param memberId 用户ID
     * @param limit 限制数量
     * @return 最新交易记录列表
     */
    @Transactional(readOnly = true)
    public List<WalletTransactionDTO> getLatestTransactionsByMember(Long memberId, int limit) {
        LOG.debug("Request to get latest transactions for member: {}, limit: {}", memberId, limit);

        Pageable pageable = PageRequest.of(0, limit);
        Page<WalletTransaction> transactions = walletTransactionRepository.findLatestTransactionsByMemberId(memberId, pageable);

        return transactions.getContent().stream().map(walletTransactionMapper::toDto).toList();
    }

    /**
     * 获取钱包指定时间后的最新交易记录
     * @param walletId 钱包ID
     * @param afterTime 时间起点
     * @param limit 限制数量
     * @return 最新交易记录列表
     */
    @Transactional(readOnly = true)
    public List<WalletTransactionDTO> getLatestTransactionsByWalletAfterTime(Long walletId, Instant afterTime, int limit) {
        LOG.debug("Request to get latest transactions for wallet: {} after time: {}, limit: {}", walletId, afterTime, limit);

        Pageable pageable = PageRequest.of(0, limit);
        Page<WalletTransaction> transactions = walletTransactionRepository.findLatestTransactionsByWalletIdAfterTime(
            walletId,
            afterTime,
            pageable
        );

        return transactions.getContent().stream().map(walletTransactionMapper::toDto).toList();
    }

    /**
     * 获取用户指定时间后的最新交易记录
     * @param memberId 用户ID
     * @param afterTime 时间起点
     * @param limit 限制数量
     * @return 最新交易记录列表
     */
    @Transactional(readOnly = true)
    public List<WalletTransactionDTO> getLatestTransactionsByMemberAfterTime(Long memberId, Instant afterTime, int limit) {
        LOG.debug("Request to get latest transactions for member: {} after time: {}, limit: {}", memberId, afterTime, limit);

        Pageable pageable = PageRequest.of(0, limit);
        Page<WalletTransaction> transactions = walletTransactionRepository.findLatestTransactionsByMemberIdAfterTime(
            memberId,
            afterTime,
            pageable
        );

        return transactions.getContent().stream().map(walletTransactionMapper::toDto).toList();
    }

    /**
     * 获取当前用户的最新交易记录（用于语音提示功能）
     * @param limit 限制数量，默认10条
     * @return 最新交易记录列表
     */
    @Transactional(readOnly = true)
    public List<WalletTransactionDTO> getCurrentUserLatestTransactions(int limit) {
        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        return getLatestTransactionsByMember(currentMemberId, limit);
    }

    /**
     * 获取当前用户指定时间后的最新交易记录（用于语音提示功能）
     * @param afterTime 时间起点
     * @param limit 限制数量，默认10条
     * @return 最新交易记录列表
     */
    @Transactional(readOnly = true)
    public List<WalletTransactionDTO> getCurrentUserLatestTransactionsAfterTime(Instant afterTime, int limit) {
        // 获取当前用户
        Long currentMemberId = MemberSecurityUtils.getCurrentMember()
            .map(Member::getId)
            .orElseThrow(() -> new BadRequestAlertException("用户未登录", "WalletTransaction", "notlogin"));

        return getLatestTransactionsByMemberAfterTime(currentMemberId, afterTime, limit);
    }
}
