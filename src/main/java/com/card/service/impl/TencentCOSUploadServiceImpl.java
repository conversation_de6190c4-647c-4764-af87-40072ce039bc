package com.card.service.impl;

import com.card.config.FileUploadProperties;
import com.card.service.FileUploadService;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.region.Region;
import java.io.IOException;
import java.io.InputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 腾讯云COS文件上传服务实现
 */
@Service("tencentCOSUploadService")
public class TencentCOSUploadServiceImpl implements FileUploadService {

    private static final Logger LOG = LoggerFactory.getLogger(TencentCOSUploadServiceImpl.class);

    private final FileUploadProperties fileUploadProperties;
    private final COSClient cosClient;

    public TencentCOSUploadServiceImpl(FileUploadProperties fileUploadProperties) {
        this.fileUploadProperties = fileUploadProperties;
        this.cosClient = initCOSClient();
    }

    /**
     * 初始化COS客户端
     */
    private COSClient initCOSClient() {
        try {
            FileUploadProperties.TencentCOS tencentConfig = fileUploadProperties.getTencent();

            // 验证配置
            if (
                !StringUtils.hasText(tencentConfig.getSecretId()) ||
                !StringUtils.hasText(tencentConfig.getSecretKey()) ||
                !StringUtils.hasText(tencentConfig.getRegion())
            ) {
                throw new IllegalArgumentException("腾讯云COS配置不完整");
            }

            // 初始化用户身份信息
            COSCredentials cred = new BasicCOSCredentials(tencentConfig.getSecretId(), tencentConfig.getSecretKey());

            // 设置bucket的地域
            Region region = new Region(tencentConfig.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);

            // 生成cos客户端
            return new COSClient(cred, clientConfig);
        } catch (Exception e) {
            LOG.error("初始化腾讯云COS客户端失败: {}", e.getMessage(), e);
            throw new RuntimeException("初始化腾讯云COS客户端失败", e);
        }
    }

    @Override
    public String uploadFile(MultipartFile file, String fileName, String path) {
        try {
            FileUploadProperties.TencentCOS tencentConfig = fileUploadProperties.getTencent();

            // 构建完整的文件路径
            String fullPath = buildFilePath(path, fileName);

            // 获取文件输入流
            InputStream inputStream = file.getInputStream();

            // 设置对象元数据
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(file.getSize());
            objectMetadata.setContentType(file.getContentType());

            // 创建上传请求
            PutObjectRequest putObjectRequest = new PutObjectRequest(tencentConfig.getBucketName(), fullPath, inputStream, objectMetadata);

            // 执行上传
            cosClient.putObject(putObjectRequest);

            // 生成访问URL
            String fileUrl = generateFileUrl(fullPath);

            LOG.info("文件上传成功: fileName={}, path={}, url={}", fileName, fullPath, fileUrl);
            return fileUrl;
        } catch (CosServiceException e) {
            LOG.error("腾讯云COS服务异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        } catch (CosClientException e) {
            LOG.error("腾讯云COS客户端异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        } catch (IOException e) {
            LOG.error("文件读取异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件读取失败", e);
        }
    }

    @Override
    public boolean deleteFile(String fileUrl) {
        try {
            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(fileUrl);
            if (!StringUtils.hasText(filePath)) {
                LOG.warn("无法从URL中提取文件路径: {}", fileUrl);
                return false;
            }

            FileUploadProperties.TencentCOS tencentConfig = fileUploadProperties.getTencent();

            // 执行删除
            cosClient.deleteObject(tencentConfig.getBucketName(), filePath);

            LOG.info("文件删除成功: {}", filePath);
            return true;
        } catch (CosServiceException e) {
            LOG.error("腾讯云COS服务异常: {}", e.getMessage(), e);
            return false;
        } catch (CosClientException e) {
            LOG.error("腾讯云COS客户端异常: {}", e.getMessage(), e);
            return false;
        } catch (Exception e) {
            LOG.error("文件删除异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String generateFileUrl(String filePath) {
        FileUploadProperties.TencentCOS tencentConfig = fileUploadProperties.getTencent();

        String domain = tencentConfig.getDomain();
        if (!StringUtils.hasText(domain)) {
            // 如果没有配置自定义域名，使用默认域名
            domain = String.format("https://%s.cos.%s.myqcloud.com", tencentConfig.getBucketName(), tencentConfig.getRegion());
        }

        // 确保域名以/结尾，文件路径不以/开头
        if (!domain.endsWith("/")) {
            domain += "/";
        }
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }

        return domain + filePath;
    }

    /**
     * 构建文件路径
     */
    private String buildFilePath(String path, String fileName) {
        FileUploadProperties.TencentCOS tencentConfig = fileUploadProperties.getTencent();
        String pathPrefix = tencentConfig.getPathPrefix();

        StringBuilder fullPath = new StringBuilder();

        // 添加路径前缀
        if (StringUtils.hasText(pathPrefix)) {
            fullPath.append(pathPrefix);
            if (!pathPrefix.endsWith("/")) {
                fullPath.append("/");
            }
        }

        // 添加具体路径
        if (StringUtils.hasText(path)) {
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            fullPath.append(path);
            if (!path.endsWith("/")) {
                fullPath.append("/");
            }
        }

        // 添加文件名
        fullPath.append(fileName);

        return fullPath.toString();
    }

    /**
     * 从URL中提取文件路径
     */
    private String extractFilePathFromUrl(String fileUrl) {
        if (!StringUtils.hasText(fileUrl)) {
            return null;
        }

        FileUploadProperties.TencentCOS tencentConfig = fileUploadProperties.getTencent();
        String domain = tencentConfig.getDomain();

        if (!StringUtils.hasText(domain)) {
            // 使用默认域名格式
            domain = String.format("https://%s.cos.%s.myqcloud.com", tencentConfig.getBucketName(), tencentConfig.getRegion());
        }

        // 从URL中移除域名部分，得到文件路径
        if (fileUrl.startsWith(domain)) {
            String filePath = fileUrl.substring(domain.length());
            return filePath.startsWith("/") ? filePath.substring(1) : filePath;
        }

        return null;
    }

    /**
     * 销毁资源
     */
    public void destroy() {
        if (cosClient != null) {
            cosClient.shutdown();
        }
    }
}
