package com.card.service.impl;

import com.card.config.ApplicationProperties;
import com.card.service.TencentSmsService;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import com.tencentcloudapi.sms.v20210111.models.SendStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 腾讯云短信服务实现
 * Tencent Cloud SMS Service Implementation
 */
@Service
@ConditionalOnProperty(name = "application.sms.provider", havingValue = "tencent")
public class TencentSmsServiceImpl implements TencentSmsService {

    private static final Logger LOG = LoggerFactory.getLogger(TencentSmsServiceImpl.class);

    private final ApplicationProperties applicationProperties;
    private final SmsClient smsClient;

    public TencentSmsServiceImpl(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
        this.smsClient = initSmsClient();
    }

    /**
     * 初始化短信客户端
     */
    private SmsClient initSmsClient() {
        try {
            ApplicationProperties.Sms.TencentSms config = applicationProperties.getSms().getTencent();

            // 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey
            Credential cred = new Credential(config.getSecretId(), config.getSecretKey());

            // 实例化一个http选项，可选，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setReqMethod("POST");
            httpProfile.setConnTimeout(30);
            httpProfile.setEndpoint("sms.tencentcloudapi.com");

            // 实例化一个client选项，可选，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setSignMethod("HmacSHA256");
            clientProfile.setHttpProfile(httpProfile);

            // 实例化要请求产品的client对象
            SmsClient client = new SmsClient(cred, config.getRegion(), clientProfile);

            LOG.info("腾讯云短信客户端初始化成功，区域: {}", config.getRegion());
            return client;
        } catch (Exception e) {
            LOG.error("腾讯云短信客户端初始化失败", e);
            throw new RuntimeException("腾讯云短信客户端初始化失败", e);
        }
    }

    @Override
    public boolean sendVerificationCode(String phone, String code) {
        return sendSms(phone, applicationProperties.getSms().getTencent().getTemplateId(), new String[] { code });
    }

    @Override
    public boolean sendSms(String phone, String templateId, String[] params) {
        return sendBatchSms(new String[] { phone }, templateId, params);
    }

    @Override
    public boolean sendBatchSms(String[] phones, String templateId, String[] params) {
        try {
            ApplicationProperties.Sms.TencentSms config = applicationProperties.getSms().getTencent();

            // 实例化一个请求对象
            SendSmsRequest req = new SendSmsRequest();

            // 设置短信应用ID
            req.setSmsSdkAppId(config.getAppId());

            // 设置短信签名内容
            req.setSignName(config.getSignName());

            // 设置模板ID
            req.setTemplateId(templateId);

            // 设置模板参数
            req.setTemplateParamSet(params);

            // 设置下发手机号码，采用E.164标准，+[国家或地区码][手机号]
            String[] phoneNumbers = new String[phones.length];
            for (int i = 0; i < phones.length; i++) {
                phoneNumbers[i] = "+86" + phones[i];
            }
            req.setPhoneNumberSet(phoneNumbers);

            // 通过client对象调用想要访问的接口，需要传入请求对象
            SendSmsResponse resp = smsClient.SendSms(req);

            // 检查发送结果
            boolean allSuccess = true;
            for (SendStatus status : resp.getSendStatusSet()) {
                if (!"Ok".equals(status.getCode())) {
                    LOG.error(
                        "短信发送失败，手机号: {}, 错误码: {}, 错误信息: {}",
                        status.getPhoneNumber(),
                        status.getCode(),
                        status.getMessage()
                    );
                    allSuccess = false;
                } else {
                    LOG.info("短信发送成功，手机号: {}, 消息ID: {}", status.getPhoneNumber(), status.getSerialNo());
                }
            }

            return allSuccess;
        } catch (TencentCloudSDKException e) {
            LOG.error("腾讯云短信发送异常，错误码: {}, 错误信息: {}", e.getErrorCode(), e.getMessage(), e);
            return false;
        } catch (Exception e) {
            LOG.error("短信发送异常", e);
            return false;
        }
    }
}
