package com.card.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.card.config.FileUploadProperties;
import com.card.service.FileUploadService;
import java.io.IOException;
import java.io.InputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 阿里云OSS文件上传服务实现
 */
@Service("aliyunOSSUploadService")
public class AliyunOSSUploadServiceImpl implements FileUploadService {

    private static final Logger LOG = LoggerFactory.getLogger(AliyunOSSUploadServiceImpl.class);

    private final FileUploadProperties fileUploadProperties;
    private final OSS ossClient;

    public AliyunOSSUploadServiceImpl(FileUploadProperties fileUploadProperties) {
        this.fileUploadProperties = fileUploadProperties;
        this.ossClient = initOSSClient();
    }

    /**
     * 初始化OSS客户端
     */
    private OSS initOSSClient() {
        try {
            FileUploadProperties.AliyunOSS aliyunConfig = fileUploadProperties.getAliyun();

            // 验证配置
            if (
                !StringUtils.hasText(aliyunConfig.getAccessKeyId()) ||
                !StringUtils.hasText(aliyunConfig.getAccessKeySecret()) ||
                !StringUtils.hasText(aliyunConfig.getEndpoint())
            ) {
                throw new IllegalArgumentException("阿里云OSS配置不完整");
            }

            // 创建OSS客户端
            return new OSSClientBuilder()
                .build(aliyunConfig.getEndpoint(), aliyunConfig.getAccessKeyId(), aliyunConfig.getAccessKeySecret());
        } catch (Exception e) {
            LOG.error("初始化阿里云OSS客户端失败: {}", e.getMessage(), e);
            throw new RuntimeException("初始化阿里云OSS客户端失败", e);
        }
    }

    @Override
    public String uploadFile(MultipartFile file, String fileName, String path) {
        try {
            FileUploadProperties.AliyunOSS aliyunConfig = fileUploadProperties.getAliyun();

            // 构建完整的文件路径
            String fullPath = buildFilePath(path, fileName);

            // 获取文件输入流
            InputStream inputStream = file.getInputStream();

            // 设置对象元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());

            // 创建上传请求
            PutObjectRequest putObjectRequest = new PutObjectRequest(aliyunConfig.getBucketName(), fullPath, inputStream, metadata);

            // 执行上传
            ossClient.putObject(putObjectRequest);

            // 生成访问URL
            String fileUrl = generateFileUrl(fullPath);

            LOG.info("文件上传成功: fileName={}, path={}, url={}", fileName, fullPath, fileUrl);
            return fileUrl;
        } catch (OSSException e) {
            LOG.error("阿里云OSS服务异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        } catch (IOException e) {
            LOG.error("文件读取异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件读取失败", e);
        } catch (Exception e) {
            LOG.error("文件上传异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    @Override
    public boolean deleteFile(String fileUrl) {
        try {
            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(fileUrl);
            if (!StringUtils.hasText(filePath)) {
                LOG.warn("无法从URL中提取文件路径: {}", fileUrl);
                return false;
            }

            FileUploadProperties.AliyunOSS aliyunConfig = fileUploadProperties.getAliyun();

            // 执行删除
            ossClient.deleteObject(aliyunConfig.getBucketName(), filePath);

            LOG.info("文件删除成功: {}", filePath);
            return true;
        } catch (OSSException e) {
            LOG.error("阿里云OSS服务异常: {}", e.getMessage(), e);
            return false;
        } catch (Exception e) {
            LOG.error("文件删除异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String generateFileUrl(String filePath) {
        FileUploadProperties.AliyunOSS aliyunConfig = fileUploadProperties.getAliyun();

        String domain = aliyunConfig.getDomain();
        if (!StringUtils.hasText(domain)) {
            // 如果没有配置自定义域名，使用默认域名
            domain = String.format("https://%s.%s", aliyunConfig.getBucketName(), aliyunConfig.getEndpoint());
        }

        // 确保域名以/结尾，文件路径不以/开头
        if (!domain.endsWith("/")) {
            domain += "/";
        }
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }

        return domain + filePath;
    }

    /**
     * 构建文件路径
     */
    private String buildFilePath(String path, String fileName) {
        FileUploadProperties.AliyunOSS aliyunConfig = fileUploadProperties.getAliyun();
        String pathPrefix = aliyunConfig.getPathPrefix();

        StringBuilder fullPath = new StringBuilder();

        // 添加路径前缀
        if (StringUtils.hasText(pathPrefix)) {
            fullPath.append(pathPrefix);
            if (!pathPrefix.endsWith("/")) {
                fullPath.append("/");
            }
        }

        // 添加具体路径
        if (StringUtils.hasText(path)) {
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            fullPath.append(path);
            if (!path.endsWith("/")) {
                fullPath.append("/");
            }
        }

        // 添加文件名
        fullPath.append(fileName);

        return fullPath.toString();
    }

    /**
     * 从URL中提取文件路径
     */
    private String extractFilePathFromUrl(String fileUrl) {
        if (!StringUtils.hasText(fileUrl)) {
            return null;
        }

        FileUploadProperties.AliyunOSS aliyunConfig = fileUploadProperties.getAliyun();
        String domain = aliyunConfig.getDomain();

        if (!StringUtils.hasText(domain)) {
            // 使用默认域名格式
            domain = String.format("https://%s.%s", aliyunConfig.getBucketName(), aliyunConfig.getEndpoint());
        }

        // 从URL中移除域名部分，得到文件路径
        if (fileUrl.startsWith(domain)) {
            String filePath = fileUrl.substring(domain.length());
            return filePath.startsWith("/") ? filePath.substring(1) : filePath;
        }

        return null;
    }

    /**
     * 销毁资源
     */
    public void destroy() {
        if (ossClient != null) {
            ossClient.shutdown();
        }
    }
}
