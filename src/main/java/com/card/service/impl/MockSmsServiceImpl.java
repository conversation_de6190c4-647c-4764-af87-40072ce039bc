package com.card.service.impl;

import com.card.config.ApplicationProperties;
import com.card.service.TencentSmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 模拟短信服务实现
 * Mock SMS Service Implementation
 */
@Service
@ConditionalOnProperty(name = "application.sms.provider", havingValue = "mock", matchIfMissing = true)
public class MockSmsServiceImpl implements TencentSmsService {

    private static final Logger LOG = LoggerFactory.getLogger(MockSmsServiceImpl.class);

    private final ApplicationProperties applicationProperties;

    public MockSmsServiceImpl(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
        LOG.info("模拟短信服务已启用，配置: {}", applicationProperties.getSms().getMock());
    }

    @Override
    public boolean sendVerificationCode(String phone, String code) {
        return sendSms(phone, "VERIFICATION_CODE", new String[] { code });
    }

    @Override
    public boolean sendSms(String phone, String templateId, String[] params) {
        return sendBatchSms(new String[] { phone }, templateId, params);
    }

    @Override
    public boolean sendBatchSms(String[] phones, String templateId, String[] params) {
        try {
            ApplicationProperties.Sms.MockSms config = applicationProperties.getSms().getMock();

            if (!config.isEnabled()) {
                LOG.warn("模拟短信服务已禁用");
                return false;
            }

            // 模拟发送延迟
            if (config.getDelay() > 0) {
                Thread.sleep(config.getDelay());
            }

            // 记录发送信息
            for (String phone : phones) {
                if (config.isLogCode() && "VERIFICATION_CODE".equals(templateId) && params.length > 0) {
                    LOG.info("【模拟短信】发送验证码到手机号: {}, 验证码: {}, 模板: {}", phone, params[0], templateId);
                } else {
                    LOG.info("【模拟短信】发送短信到手机号: {}, 模板: {}, 参数: {}", phone, templateId, java.util.Arrays.toString(params));
                }
            }

            // 模拟成功率（可配置）
            return true;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOG.error("模拟短信发送被中断", e);
            return false;
        } catch (Exception e) {
            LOG.error("模拟短信发送异常", e);
            return false;
        }
    }
}
