package com.card.service;

import com.card.domain.ReferralRewardConfig;
import com.card.repository.ReferralRewardConfigRepository;
import com.card.service.dto.ReferralRewardConfigDTO;
import com.card.service.mapper.ReferralRewardConfigMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 管理推荐奖励配置的服务实现。
 * Service Implementation for managing {@link com.card.domain.ReferralRewardConfig}.
 */
@Service
@Transactional
public class ReferralRewardConfigService {

    private static final Logger LOG = LoggerFactory.getLogger(ReferralRewardConfigService.class);

    private final ReferralRewardConfigRepository referralRewardConfigRepository;

    private final ReferralRewardConfigMapper referralRewardConfigMapper;

    public ReferralRewardConfigService(
        ReferralRewardConfigRepository referralRewardConfigRepository,
        ReferralRewardConfigMapper referralRewardConfigMapper
    ) {
        this.referralRewardConfigRepository = referralRewardConfigRepository;
        this.referralRewardConfigMapper = referralRewardConfigMapper;
    }

    /**
     * 保存推荐奖励配置。
     * Save a referralRewardConfig.
     *
     * @param referralRewardConfigDTO 要保存的实体
     * @return 已持久化的实体
     */
    public ReferralRewardConfigDTO save(ReferralRewardConfigDTO referralRewardConfigDTO) {
        LOG.debug("Request to save ReferralRewardConfig : {}", referralRewardConfigDTO);
        ReferralRewardConfig referralRewardConfig = referralRewardConfigMapper.toEntity(referralRewardConfigDTO);
        referralRewardConfig = referralRewardConfigRepository.save(referralRewardConfig);
        return referralRewardConfigMapper.toDto(referralRewardConfig);
    }

    /**
     * 更新推荐奖励配置。
     * Update a referralRewardConfig.
     *
     * @param referralRewardConfigDTO 要保存的实体
     * @return 已持久化的实体
     */
    public ReferralRewardConfigDTO update(ReferralRewardConfigDTO referralRewardConfigDTO) {
        LOG.debug("Request to update ReferralRewardConfig : {}", referralRewardConfigDTO);
        ReferralRewardConfig referralRewardConfig = referralRewardConfigMapper.toEntity(referralRewardConfigDTO);
        referralRewardConfig = referralRewardConfigRepository.save(referralRewardConfig);
        return referralRewardConfigMapper.toDto(referralRewardConfig);
    }

    /**
     * 部分更新推荐奖励配置。
     * Partially update a referralRewardConfig.
     *
     * @param referralRewardConfigDTO 要部分更新的实体
     * @return 已持久化的实体
     */
    public Optional<ReferralRewardConfigDTO> partialUpdate(ReferralRewardConfigDTO referralRewardConfigDTO) {
        LOG.debug("Request to partially update ReferralRewardConfig : {}", referralRewardConfigDTO);

        return referralRewardConfigRepository
            .findById(referralRewardConfigDTO.getId())
            .map(existingReferralRewardConfig -> {
                referralRewardConfigMapper.partialUpdate(existingReferralRewardConfig, referralRewardConfigDTO);

                return existingReferralRewardConfig;
            })
            .map(referralRewardConfigRepository::save)
            .map(referralRewardConfigMapper::toDto);
    }

    /**
     * 获取所有推荐奖励配置。
     * Get all the referralRewardConfigs.
     *
     * @param pageable 分页信息
     * @return 实体列表
     */
    @Transactional(readOnly = true)
    public Page<ReferralRewardConfigDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all ReferralRewardConfigs");
        return referralRewardConfigRepository.findAll(pageable).map(referralRewardConfigMapper::toDto);
    }

    /**
     * 根据ID获取推荐奖励配置。
     * Get one referralRewardConfig by id.
     *
     * @param id 实体的ID
     * @return 实体
     */
    @Transactional(readOnly = true)
    public Optional<ReferralRewardConfigDTO> findOne(Long id) {
        LOG.debug("Request to get ReferralRewardConfig : {}", id);
        return referralRewardConfigRepository.findById(id).map(referralRewardConfigMapper::toDto);
    }

    /**
     * 根据ID删除推荐奖励配置。
     * Delete the referralRewardConfig by id.
     *
     * @param id 实体的ID
     */
    public void delete(Long id) {
        LOG.debug("Request to delete ReferralRewardConfig : {}", id);
        referralRewardConfigRepository.deleteById(id);
    }
}
