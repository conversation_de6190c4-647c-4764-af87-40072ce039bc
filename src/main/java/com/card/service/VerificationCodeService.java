package com.card.service;

import com.card.service.TencentSmsService;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 验证码管理服务
 * Verification Code Management Service
 */
@Service
@ConditionalOnBean(StringRedisTemplate.class)
public class VerificationCodeService implements VerificationCodeServiceInterface {

    private static final Logger LOG = LoggerFactory.getLogger(VerificationCodeService.class);

    /**
     * 验证码Redis Key前缀
     */
    private static final String VERIFY_CODE_PREFIX = "verify_code:";

    /**
     * 发送限制Redis Key前缀
     */
    private static final String SEND_LIMIT_PREFIX = "verify_code:limit:";

    /**
     * 验证码过期时间（秒）
     */
    private static final int CODE_EXPIRE_SECONDS = 300; // 5分钟

    /**
     * 发送限制时间（秒）
     */
    private static final int SEND_LIMIT_SECONDS = 60; // 60秒内不能重复发送

    private final StringRedisTemplate redisTemplate;
    private final TencentSmsService smsService;

    public VerificationCodeService(StringRedisTemplate redisTemplate, TencentSmsService smsService) {
        this.redisTemplate = redisTemplate;
        this.smsService = smsService;
    }

    /**
     * 发送验证码
     *
     * @param phone 手机号
     * @param type 验证码类型
     * @return 发送结果
     */
    public Map<String, Object> sendVerificationCode(String phone, String type) {
        LOG.debug("发送验证码请求: phone={}, type={}", phone, type);

        Map<String, Object> result = new HashMap<>();

        try {
            // 检查发送频率限制
            String limitKey = SEND_LIMIT_PREFIX + phone;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(limitKey))) {
                Long ttl = redisTemplate.getExpire(limitKey);
                result.put("success", false);
                result.put("message", "发送过于频繁，请" + ttl + "秒后再试");
                result.put("canResendAfter", ttl);
                return result;
            }

            // 生成6位随机验证码
            String verifyCode = generateVerificationCode();

            // 发送短信
            boolean sendSuccess = smsService.sendVerificationCode(phone, verifyCode);
            if (!sendSuccess) {
                result.put("success", false);
                result.put("message", "短信发送失败，请稍后重试");
                return result;
            }

            // 存储验证码到Redis
            String codeKey = VERIFY_CODE_PREFIX + type + ":" + phone;
            redisTemplate.opsForValue().set(codeKey, verifyCode, Duration.ofSeconds(CODE_EXPIRE_SECONDS));

            // 设置发送限制
            redisTemplate.opsForValue().set(limitKey, String.valueOf(System.currentTimeMillis()), Duration.ofSeconds(SEND_LIMIT_SECONDS));

            LOG.info("验证码发送成功: phone={}, type={}", phone, type);

            result.put("success", true);
            result.put("message", "验证码发送成功");
            result.put("phone", phone);
            result.put("expireTime", CODE_EXPIRE_SECONDS);
            result.put("canResendAfter", SEND_LIMIT_SECONDS);

            return result;
        } catch (Exception e) {
            LOG.error("发送验证码异常: phone={}, type={}", phone, type, e);
            result.put("success", false);
            result.put("message", "系统异常，请稍后重试");
            return result;
        }
    }

    /**
     * 验证验证码
     *
     * @param phone 手机号
     * @param type 验证码类型
     * @param code 验证码
     * @return 验证是否成功
     */
    public boolean verifyCode(String phone, String type, String code) {
        LOG.debug("验证验证码: phone={}, type={}, code={}", phone, type, code);

        if (!StringUtils.hasText(phone) || !StringUtils.hasText(type) || !StringUtils.hasText(code)) {
            LOG.warn("验证码验证参数不完整");
            return false;
        }

        try {
            String codeKey = VERIFY_CODE_PREFIX + type + ":" + phone;
            String storedCode = redisTemplate.opsForValue().get(codeKey);

            if (!StringUtils.hasText(storedCode)) {
                LOG.warn("验证码不存在或已过期: phone={}, type={}", phone, type);
                return false;
            }

            boolean isValid = code.equals(storedCode);
            if (isValid) {
                // 验证成功后删除验证码
                redisTemplate.delete(codeKey);
                LOG.info("验证码验证成功: phone={}, type={}", phone, type);
            } else {
                LOG.warn("验证码错误: phone={}, type={}, expected={}, actual={}", phone, type, storedCode, code);
            }

            return isValid;
        } catch (Exception e) {
            LOG.error("验证码验证异常: phone={}, type={}, code={}", phone, type, code, e);
            return false;
        }
    }

    /**
     * 清除验证码
     *
     * @param phone 手机号
     * @param type 验证码类型
     */
    public void clearVerificationCode(String phone, String type) {
        try {
            String codeKey = VERIFY_CODE_PREFIX + type + ":" + phone;
            redisTemplate.delete(codeKey);
            LOG.debug("清除验证码: phone={}, type={}", phone, type);
        } catch (Exception e) {
            LOG.error("清除验证码异常: phone={}, type={}", phone, type, e);
        }
    }

    /**
     * 生成6位随机验证码
     */
    private String generateVerificationCode() {
        return String.format("%06d", ThreadLocalRandom.current().nextInt(100000, 1000000));
    }

    /**
     * 检查是否可以发送验证码
     *
     * @param phone 手机号
     * @return 检查结果
     */
    public Map<String, Object> checkSendLimit(String phone) {
        Map<String, Object> result = new HashMap<>();

        try {
            String limitKey = SEND_LIMIT_PREFIX + phone;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(limitKey))) {
                Long ttl = redisTemplate.getExpire(limitKey);
                result.put("canSend", false);
                result.put("remainingSeconds", ttl);
            } else {
                result.put("canSend", true);
                result.put("remainingSeconds", 0);
            }
        } catch (Exception e) {
            LOG.error("检查发送限制异常: phone={}", phone, e);
            result.put("canSend", true);
            result.put("remainingSeconds", 0);
        }

        return result;
    }
}
