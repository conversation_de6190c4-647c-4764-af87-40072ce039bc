package com.card.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

/**
 * 注册响应DTO
 * DTO for registration response
 */
@Schema(description = "注册响应数据")
public class RegisterResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作是否成功
     */
    @Schema(description = "操作是否成功", example = "true")
    private boolean success;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息", example = "注册成功")
    private String message;

    /**
     * 会员信息
     */
    @Schema(description = "会员信息")
    private MemberDTO member;

    /**
     * JWT Token
     */
    @Schema(description = "JWT认证令牌", example = "Member-eyJhbGciOiJIUzI1NiJ9...")
    private String token;

    /**
     * Token过期时间（秒）
     */
    @Schema(description = "Token过期时间（秒）", example = "86400")
    private long expiresIn;

    /**
     * 是否为新用户（仅微信注册时使用）
     */
    @Schema(description = "是否为新用户", example = "true")
    private Boolean isNewUser;

    // 构造函数
    public RegisterResponseDTO() {}

    public RegisterResponseDTO(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    // 静态工厂方法
    public static RegisterResponseDTO success(String message, MemberDTO member, String token, long expiresIn) {
        RegisterResponseDTO response = new RegisterResponseDTO(true, message);
        response.setMember(member);
        response.setToken(token);
        response.setExpiresIn(expiresIn);
        return response;
    }

    public static RegisterResponseDTO success(String message, MemberDTO member, String token, long expiresIn, boolean isNewUser) {
        RegisterResponseDTO response = success(message, member, token, expiresIn);
        response.setIsNewUser(isNewUser);
        return response;
    }

    public static RegisterResponseDTO error(String message) {
        return new RegisterResponseDTO(false, message);
    }

    // Getter和Setter方法
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public MemberDTO getMember() {
        return member;
    }

    public void setMember(MemberDTO member) {
        this.member = member;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(long expiresIn) {
        this.expiresIn = expiresIn;
    }

    public Boolean getIsNewUser() {
        return isNewUser;
    }

    public void setIsNewUser(Boolean isNewUser) {
        this.isNewUser = isNewUser;
    }

    @Override
    public String toString() {
        return (
            "RegisterResponseDTO{" +
            "success=" +
            success +
            ", message='" +
            message +
            '\'' +
            ", member=" +
            (member != null ? member.getUuid() : null) +
            ", hasToken=" +
            (token != null) +
            ", expiresIn=" +
            expiresIn +
            ", isNewUser=" +
            isNewUser +
            '}'
        );
    }
}
