package com.card.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.card.domain.ReferralCodeManagement} entity.
 */
@Schema(description = "推荐码管理实体\n管理推荐码的生成、使用、过期等")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ReferralCodeManagementDTO implements Serializable {

    private Long id;

    @NotNull
    @Schema(description = "业务唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uuid;

    @NotNull
    @Size(max = 10)
    @Schema(description = "推荐码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String referralCode;

    @NotNull
    @Min(value = 1)
    @Max(value = 3)
    @Schema(description = "推荐码类型：1-个人专属 2-临时活动 3-批量生成", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer codeType;

    @Min(value = 0)
    @Schema(description = "使用次数限制（0表示无限制）")
    private Integer usageLimit;

    @Min(value = 0)
    @Schema(description = "已使用次数")
    private Integer usedCount;

    @Min(value = 0)
    @Max(value = 2)
    @Schema(description = "推荐码状态：0-禁用 1-启用 2-过期")
    private Integer status;

    @NotNull
    @Schema(description = "生效时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant effectiveTime;

    @Schema(description = "过期时间")
    private Instant expireTime;

    @Size(max = 255)
    @Schema(description = "推荐码说明")
    private String description;

    @Schema(description = "逻辑删除标记")
    private Boolean deleted;

    @NotNull
    @Schema(description = "记录创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createTime;

    @Schema(description = "记录更新时间")
    private Instant updateTime;

    private MemberDTO member;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getReferralCode() {
        return referralCode;
    }

    public void setReferralCode(String referralCode) {
        this.referralCode = referralCode;
    }

    public Integer getCodeType() {
        return codeType;
    }

    public void setCodeType(Integer codeType) {
        this.codeType = codeType;
    }

    public Integer getUsageLimit() {
        return usageLimit;
    }

    public void setUsageLimit(Integer usageLimit) {
        this.usageLimit = usageLimit;
    }

    public Integer getUsedCount() {
        return usedCount;
    }

    public void setUsedCount(Integer usedCount) {
        this.usedCount = usedCount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Instant getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Instant effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Instant getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Instant getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public Instant getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Instant updateTime) {
        this.updateTime = updateTime;
    }

    public MemberDTO getMember() {
        return member;
    }

    public void setMember(MemberDTO member) {
        this.member = member;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ReferralCodeManagementDTO)) {
            return false;
        }

        ReferralCodeManagementDTO referralCodeManagementDTO = (ReferralCodeManagementDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, referralCodeManagementDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ReferralCodeManagementDTO{" +
            "id=" + getId() +
            ", uuid='" + getUuid() + "'" +
            ", referralCode='" + getReferralCode() + "'" +
            ", codeType=" + getCodeType() +
            ", usageLimit=" + getUsageLimit() +
            ", usedCount=" + getUsedCount() +
            ", status=" + getStatus() +
            ", effectiveTime='" + getEffectiveTime() + "'" +
            ", expireTime='" + getExpireTime() + "'" +
            ", description='" + getDescription() + "'" +
            ", deleted='" + getDeleted() + "'" +
            ", createTime='" + getCreateTime() + "'" +
            ", updateTime='" + getUpdateTime() + "'" +
            ", member=" + getMember() +
            "}";
    }
}
