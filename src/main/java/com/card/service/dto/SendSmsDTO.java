package com.card.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 发送短信验证码请求DTO
 * DTO for sending SMS verification code request
 */
@Schema(description = "发送短信验证码请求参数")
public class SendSmsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号码", example = "13800138000", requiredMode = Schema.RequiredMode.REQUIRED)
    private String phone;

    /**
     * 验证码类型
     */
    @NotBlank(message = "验证码类型不能为空")
    @Pattern(regexp = "^(register|login|reset_password)$", message = "验证码类型只能是register、login或reset_password")
    @Schema(
        description = "验证码类型",
        example = "register",
        allowableValues = { "register", "login", "reset_password" },
        requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String type;

    // 构造函数
    public SendSmsDTO() {}

    public SendSmsDTO(String phone, String type) {
        this.phone = phone;
        this.type = type;
    }

    // Getter和Setter方法
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SendSmsDTO)) return false;
        SendSmsDTO that = (SendSmsDTO) o;
        return java.util.Objects.equals(phone, that.phone) && java.util.Objects.equals(type, that.type);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(phone, type);
    }

    @Override
    public String toString() {
        return "SendSmsDTO{" + "phone='" + phone + '\'' + ", type='" + type + '\'' + '}';
    }
}
