package com.card.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 手机号注册请求DTO
 * DTO for phone number registration request
 */
@Schema(description = "手机号注册请求参数")
public class PhoneRegisterDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号码", example = "13800138000", requiredMode = Schema.RequiredMode.REQUIRED)
    private String phone;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    @Schema(description = "登录密码", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    private String password;

    /**
     * 确认密码
     */
    @NotBlank(message = "确认密码不能为空")
    @Schema(description = "确认密码", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    private String confirmPassword;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码必须是6位数字")
    @Schema(description = "短信验证码", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    private String verifyCode;

    /**
     * 用户昵称（可选）
     */
    @Size(max = 64, message = "昵称长度不能超过64个字符")
    @Schema(description = "用户昵称", example = "张三")
    private String nickname;

    /**
     * 推荐人UUID（可选）
     */
    @Size(max = 36, message = "推荐人UUID长度不能超过36个字符")
    @Schema(description = "推荐人UUID", example = "550e8400-e29b-41d4-a716-************")
    private String referrerUuid;

    /**
     * 推荐码（可选）
     */
    @Size(max = 10, message = "推荐码长度不能超过10个字符")
    @Schema(description = "推荐码", example = "ABC123")
    private String referralCode;

    // 构造函数
    public PhoneRegisterDTO() {}

    // Getter和Setter方法
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getConfirmPassword() {
        return confirmPassword;
    }

    public void setConfirmPassword(String confirmPassword) {
        this.confirmPassword = confirmPassword;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getReferrerUuid() {
        return referrerUuid;
    }

    public void setReferrerUuid(String referrerUuid) {
        this.referrerUuid = referrerUuid;
    }

    public String getReferralCode() {
        return referralCode;
    }

    public void setReferralCode(String referralCode) {
        this.referralCode = referralCode;
    }

    /**
     * 验证密码是否一致
     */
    public boolean isPasswordMatch() {
        return password != null && password.equals(confirmPassword);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PhoneRegisterDTO)) return false;
        PhoneRegisterDTO that = (PhoneRegisterDTO) o;
        return java.util.Objects.equals(phone, that.phone);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(phone);
    }

    @Override
    public String toString() {
        return (
            "PhoneRegisterDTO{" +
            "phone='" +
            phone +
            '\'' +
            ", nickname='" +
            nickname +
            '\'' +
            ", referrerUuid='" +
            referrerUuid +
            '\'' +
            ", referralCode='" +
            referralCode +
            '\'' +
            '}'
        );
    }
}
