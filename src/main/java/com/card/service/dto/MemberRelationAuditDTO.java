package com.card.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Lob;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.card.domain.MemberRelationAudit} entity.
 */
@Schema(description = "推荐关系审计日志\n记录推荐关系的所有变更操作")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class MemberRelationAuditDTO implements Serializable {

    private Long id;

    @NotNull
    @Schema(description = "业务唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uuid;

    @NotNull
    @Schema(description = "关联的推荐关系ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long relationId;

    @NotNull
    @Min(value = 1)
    @Max(value = 5)
    @Schema(description = "操作类型：1-创建 2-更新 3-删除 4-冻结 5-解冻", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer operationType;

    @Schema(description = "操作前数据（JSON格式）")
    @Lob
    private String beforeData;

    @Schema(description = "操作后数据（JSON格式）")
    @Lob
    private String afterData;

    @Size(max = 500)
    @Schema(description = "操作原因")
    private String operationReason;

    @Schema(description = "操作人员ID")
    private Long operatorId;

    @Size(max = 50)
    @Schema(description = "操作人员姓名")
    private String operatorName;

    @Size(max = 45)
    @Schema(description = "操作IP地址")
    private String operatorIp;

    @NotNull
    @Schema(description = "操作时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant operationTime;

    private MemberRelationsDTO relation;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Long getRelationId() {
        return relationId;
    }

    public void setRelationId(Long relationId) {
        this.relationId = relationId;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getBeforeData() {
        return beforeData;
    }

    public void setBeforeData(String beforeData) {
        this.beforeData = beforeData;
    }

    public String getAfterData() {
        return afterData;
    }

    public void setAfterData(String afterData) {
        this.afterData = afterData;
    }

    public String getOperationReason() {
        return operationReason;
    }

    public void setOperationReason(String operationReason) {
        this.operationReason = operationReason;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOperatorIp() {
        return operatorIp;
    }

    public void setOperatorIp(String operatorIp) {
        this.operatorIp = operatorIp;
    }

    public Instant getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Instant operationTime) {
        this.operationTime = operationTime;
    }

    public MemberRelationsDTO getRelation() {
        return relation;
    }

    public void setRelation(MemberRelationsDTO relation) {
        this.relation = relation;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MemberRelationAuditDTO)) {
            return false;
        }

        MemberRelationAuditDTO memberRelationAuditDTO = (MemberRelationAuditDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, memberRelationAuditDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "MemberRelationAuditDTO{" +
            "id=" + getId() +
            ", uuid='" + getUuid() + "'" +
            ", relationId=" + getRelationId() +
            ", operationType=" + getOperationType() +
            ", beforeData='" + getBeforeData() + "'" +
            ", afterData='" + getAfterData() + "'" +
            ", operationReason='" + getOperationReason() + "'" +
            ", operatorId=" + getOperatorId() +
            ", operatorName='" + getOperatorName() + "'" +
            ", operatorIp='" + getOperatorIp() + "'" +
            ", operationTime='" + getOperationTime() + "'" +
            ", relation=" + getRelation() +
            "}";
    }
}
