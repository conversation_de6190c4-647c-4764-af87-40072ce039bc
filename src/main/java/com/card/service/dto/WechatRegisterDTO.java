package com.card.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 微信注册请求DTO
 * DTO for WeChat registration request
 */
@Schema(description = "微信注册请求参数")
public class WechatRegisterDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 微信授权码
     */
    @NotBlank(message = "微信授权码不能为空")
    @Schema(description = "微信小程序授权码", example = "0c1a2b3c4d5e6f7g8h9i", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    /**
     * 加密的用户信息（可选）
     */
    @Schema(description = "加密的用户信息")
    private String encryptedData;

    /**
     * 初始向量（可选）
     */
    @Schema(description = "初始向量")
    private String iv;

    /**
     * 推荐人UUID（可选）
     */
    @Size(max = 36, message = "推荐人UUID长度不能超过36个字符")
    @Schema(description = "推荐人UUID", example = "550e8400-e29b-41d4-a716-************")
    private String referrerUuid;

    /**
     * 推荐码（可选）
     */
    @Size(max = 10, message = "推荐码长度不能超过10个字符")
    @Schema(description = "推荐码", example = "ABC123")
    private String referralCode;

    // 构造函数
    public WechatRegisterDTO() {}

    public WechatRegisterDTO(String code) {
        this.code = code;
    }

    // Getter和Setter方法
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEncryptedData() {
        return encryptedData;
    }

    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData;
    }

    public String getIv() {
        return iv;
    }

    public void setIv(String iv) {
        this.iv = iv;
    }

    public String getReferrerUuid() {
        return referrerUuid;
    }

    public void setReferrerUuid(String referrerUuid) {
        this.referrerUuid = referrerUuid;
    }

    public String getReferralCode() {
        return referralCode;
    }

    public void setReferralCode(String referralCode) {
        this.referralCode = referralCode;
    }

    /**
     * 检查是否有加密数据
     */
    public boolean hasEncryptedData() {
        return encryptedData != null && !encryptedData.trim().isEmpty() && iv != null && !iv.trim().isEmpty();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof WechatRegisterDTO)) return false;
        WechatRegisterDTO that = (WechatRegisterDTO) o;
        return java.util.Objects.equals(code, that.code);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(code);
    }

    @Override
    public String toString() {
        return (
            "WechatRegisterDTO{" +
            "code='" +
            code +
            '\'' +
            ", hasEncryptedData=" +
            hasEncryptedData() +
            ", referrerUuid='" +
            referrerUuid +
            '\'' +
            ", referralCode='" +
            referralCode +
            '\'' +
            '}'
        );
    }
}
