package com.card.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.card.domain.MemberRelations} entity.
 */
@Schema(description = "会员关系实体 - 会员层级关系表\n存储会员推荐关系、团队结构等层级信息")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class MemberRelationsDTO implements Serializable {

    private Long id;

    @NotNull
    @Schema(description = "业务唯一标识（32位UUID，用于对外接口）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uuid;

    @NotNull
    @Schema(description = "会员ID（当前会员）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long memberId;

    @NotNull
    @Schema(description = "上级会员ID（推荐人/上级）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long parentId;

    @Size(max = 10)
    @Schema(description = "推荐码（用于记录通过哪个推荐码建立的关系）")
    private String referralCode;

    @NotNull
    @Min(value = 1)
    @Max(value = 4)
    @Schema(description = "关系类型：1-推荐人 2-团队长 3-代理商 4-合作伙伴", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer relationType;

    @Min(value = 1)
    @Max(value = 10)
    @Schema(description = "层级深度（1-直接推荐，2-二级推荐，以此类推）")
    private Integer level;

    @Size(max = 1000)
    @Schema(description = "完整路径（用/分隔的ID链，便于快速查询）")
    private String relationPath;

    @Min(value = 0)
    @Max(value = 3)
    @Schema(description = "关系状态：0-失效 1-有效 2-暂停 3-冻结")
    private Integer status;

    @DecimalMin(value = "0")
    @Schema(description = "推荐奖励金额（单位：分）")
    private BigDecimal rewardAmount;

    @Min(value = 0)
    @Max(value = 3)
    @Schema(description = "奖励状态：0-未发放 1-已发放 2-已取消 3-计算中")
    private Integer rewardStatus;

    @Schema(description = "奖励发放时间")
    private Instant rewardTime;

    @NotNull
    @Schema(description = "关系生效时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant effectiveTime;

    @Schema(description = "关系失效时间")
    private Instant expireTime;

    @Size(max = 255)
    @Schema(description = "冻结原因")
    private String frozenReason;

    @Schema(description = "冻结时间")
    private Instant frozenTime;

    @Schema(description = "解冻时间")
    private Instant unfrozenTime;

    @Size(max = 500)
    @Schema(description = "备注说明")
    private String remark;

    @Min(value = 1)
    @Max(value = 3)
    @Schema(description = "数据来源：1-系统自动 2-手动录入 3-批量导入")
    private Integer dataSource;

    @Schema(description = "操作人员ID")
    private Long operatorId;

    @Schema(description = "逻辑删除标记：false-正常 true-已删除")
    private Boolean deleted;

    @NotNull
    @Schema(description = "记录创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createTime;

    @Schema(description = "记录更新时间")
    private Instant updateTime;

    private MemberDTO member;

    private MemberDTO parent;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getReferralCode() {
        return referralCode;
    }

    public void setReferralCode(String referralCode) {
        this.referralCode = referralCode;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getRelationPath() {
        return relationPath;
    }

    public void setRelationPath(String relationPath) {
        this.relationPath = relationPath;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getRewardAmount() {
        return rewardAmount;
    }

    public void setRewardAmount(BigDecimal rewardAmount) {
        this.rewardAmount = rewardAmount;
    }

    public Integer getRewardStatus() {
        return rewardStatus;
    }

    public void setRewardStatus(Integer rewardStatus) {
        this.rewardStatus = rewardStatus;
    }

    public Instant getRewardTime() {
        return rewardTime;
    }

    public void setRewardTime(Instant rewardTime) {
        this.rewardTime = rewardTime;
    }

    public Instant getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Instant effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Instant getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public String getFrozenReason() {
        return frozenReason;
    }

    public void setFrozenReason(String frozenReason) {
        this.frozenReason = frozenReason;
    }

    public Instant getFrozenTime() {
        return frozenTime;
    }

    public void setFrozenTime(Instant frozenTime) {
        this.frozenTime = frozenTime;
    }

    public Instant getUnfrozenTime() {
        return unfrozenTime;
    }

    public void setUnfrozenTime(Instant unfrozenTime) {
        this.unfrozenTime = unfrozenTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDataSource() {
        return dataSource;
    }

    public void setDataSource(Integer dataSource) {
        this.dataSource = dataSource;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Instant getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public Instant getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Instant updateTime) {
        this.updateTime = updateTime;
    }

    public MemberDTO getMember() {
        return member;
    }

    public void setMember(MemberDTO member) {
        this.member = member;
    }

    public MemberDTO getParent() {
        return parent;
    }

    public void setParent(MemberDTO parent) {
        this.parent = parent;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MemberRelationsDTO)) {
            return false;
        }

        MemberRelationsDTO memberRelationsDTO = (MemberRelationsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, memberRelationsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "MemberRelationsDTO{" +
            "id=" + getId() +
            ", uuid='" + getUuid() + "'" +
            ", memberId=" + getMemberId() +
            ", parentId=" + getParentId() +
            ", referralCode='" + getReferralCode() + "'" +
            ", relationType=" + getRelationType() +
            ", level=" + getLevel() +
            ", relationPath='" + getRelationPath() + "'" +
            ", status=" + getStatus() +
            ", rewardAmount=" + getRewardAmount() +
            ", rewardStatus=" + getRewardStatus() +
            ", rewardTime='" + getRewardTime() + "'" +
            ", effectiveTime='" + getEffectiveTime() + "'" +
            ", expireTime='" + getExpireTime() + "'" +
            ", frozenReason='" + getFrozenReason() + "'" +
            ", frozenTime='" + getFrozenTime() + "'" +
            ", unfrozenTime='" + getUnfrozenTime() + "'" +
            ", remark='" + getRemark() + "'" +
            ", dataSource=" + getDataSource() +
            ", operatorId=" + getOperatorId() +
            ", deleted='" + getDeleted() + "'" +
            ", createTime='" + getCreateTime() + "'" +
            ", updateTime='" + getUpdateTime() + "'" +
            ", member=" + getMember() +
            ", parent=" + getParent() +
            "}";
    }
}
