package com.card.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.card.domain.MemberReferralStats} entity.
 */
@Schema(description = "会员推荐统计实体 - 推荐业绩统计\n存储会员推荐数据的统计信息，便于快速查询")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class MemberReferralStatsDTO implements Serializable {

    private Long id;

    @NotNull
    @Schema(description = "业务唯一标识（32位UUID）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uuid;

    @Min(value = 0)
    @Schema(description = "直接推荐人数")
    private Integer directReferrals;

    @Min(value = 0)
    @Schema(description = "间接推荐人数（团队总人数）")
    private Integer indirectReferrals;

    @Min(value = 0)
    @Schema(description = "有效推荐人数（状态为有效的推荐）")
    private Integer activeReferrals;

    @DecimalMin(value = "0")
    @Schema(description = "总推荐奖励金额（单位：分）")
    private BigDecimal totalReward;

    @DecimalMin(value = "0")
    @Schema(description = "已发放奖励金额")
    private BigDecimal paidReward;

    @DecimalMin(value = "0")
    @Schema(description = "待发放奖励金额")
    private BigDecimal pendingReward;

    @Min(value = 0)
    @Schema(description = "本月推荐人数")
    private Integer monthlyReferrals;

    @DecimalMin(value = "0")
    @Schema(description = "本月推荐奖励（单位：分）")
    private BigDecimal monthlyReward;

    @Min(value = 0)
    @Schema(description = "本周推荐人数")
    private Integer weeklyReferrals;

    @Min(value = 0)
    @Schema(description = "今日推荐人数")
    private Integer dailyReferrals;

    @NotNull
    @Size(max = 7)
    @Schema(description = "统计月份（格式：YYYY-MM）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statMonth;

    @Min(value = 0)
    @Schema(description = "团队层级深度")
    private Integer maxTeamLevel;

    @Schema(description = "逻辑删除标记：false-正常 true-已删除")
    private Boolean deleted;

    @NotNull
    @Schema(description = "记录创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createTime;

    @Schema(description = "记录更新时间")
    private Instant updateTime;

    private MemberDTO member;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getDirectReferrals() {
        return directReferrals;
    }

    public void setDirectReferrals(Integer directReferrals) {
        this.directReferrals = directReferrals;
    }

    public Integer getIndirectReferrals() {
        return indirectReferrals;
    }

    public void setIndirectReferrals(Integer indirectReferrals) {
        this.indirectReferrals = indirectReferrals;
    }

    public Integer getActiveReferrals() {
        return activeReferrals;
    }

    public void setActiveReferrals(Integer activeReferrals) {
        this.activeReferrals = activeReferrals;
    }

    public BigDecimal getTotalReward() {
        return totalReward;
    }

    public void setTotalReward(BigDecimal totalReward) {
        this.totalReward = totalReward;
    }

    public BigDecimal getPaidReward() {
        return paidReward;
    }

    public void setPaidReward(BigDecimal paidReward) {
        this.paidReward = paidReward;
    }

    public BigDecimal getPendingReward() {
        return pendingReward;
    }

    public void setPendingReward(BigDecimal pendingReward) {
        this.pendingReward = pendingReward;
    }

    public Integer getMonthlyReferrals() {
        return monthlyReferrals;
    }

    public void setMonthlyReferrals(Integer monthlyReferrals) {
        this.monthlyReferrals = monthlyReferrals;
    }

    public BigDecimal getMonthlyReward() {
        return monthlyReward;
    }

    public void setMonthlyReward(BigDecimal monthlyReward) {
        this.monthlyReward = monthlyReward;
    }

    public Integer getWeeklyReferrals() {
        return weeklyReferrals;
    }

    public void setWeeklyReferrals(Integer weeklyReferrals) {
        this.weeklyReferrals = weeklyReferrals;
    }

    public Integer getDailyReferrals() {
        return dailyReferrals;
    }

    public void setDailyReferrals(Integer dailyReferrals) {
        this.dailyReferrals = dailyReferrals;
    }

    public String getStatMonth() {
        return statMonth;
    }

    public void setStatMonth(String statMonth) {
        this.statMonth = statMonth;
    }

    public Integer getMaxTeamLevel() {
        return maxTeamLevel;
    }

    public void setMaxTeamLevel(Integer maxTeamLevel) {
        this.maxTeamLevel = maxTeamLevel;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Instant getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public Instant getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Instant updateTime) {
        this.updateTime = updateTime;
    }

    public MemberDTO getMember() {
        return member;
    }

    public void setMember(MemberDTO member) {
        this.member = member;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MemberReferralStatsDTO)) {
            return false;
        }

        MemberReferralStatsDTO memberReferralStatsDTO = (MemberReferralStatsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, memberReferralStatsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "MemberReferralStatsDTO{" +
            "id=" + getId() +
            ", uuid='" + getUuid() + "'" +
            ", directReferrals=" + getDirectReferrals() +
            ", indirectReferrals=" + getIndirectReferrals() +
            ", activeReferrals=" + getActiveReferrals() +
            ", totalReward=" + getTotalReward() +
            ", paidReward=" + getPaidReward() +
            ", pendingReward=" + getPendingReward() +
            ", monthlyReferrals=" + getMonthlyReferrals() +
            ", monthlyReward=" + getMonthlyReward() +
            ", weeklyReferrals=" + getWeeklyReferrals() +
            ", dailyReferrals=" + getDailyReferrals() +
            ", statMonth='" + getStatMonth() + "'" +
            ", maxTeamLevel=" + getMaxTeamLevel() +
            ", deleted='" + getDeleted() + "'" +
            ", createTime='" + getCreateTime() + "'" +
            ", updateTime='" + getUpdateTime() + "'" +
            ", member=" + getMember() +
            "}";
    }
}
