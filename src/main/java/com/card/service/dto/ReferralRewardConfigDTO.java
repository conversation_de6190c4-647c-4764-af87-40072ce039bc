package com.card.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.card.domain.ReferralRewardConfig} entity.
 */
@Schema(description = "推荐奖励配置实体\n存储不同层级、不同类型的奖励规则配置")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ReferralRewardConfigDTO implements Serializable {

    private Long id;

    @NotNull
    @Schema(description = "业务唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uuid;

    @NotNull
    @Size(max = 100)
    @Schema(description = "配置名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String configName;

    @Min(value = 0)
    @Max(value = 10)
    @Schema(description = "会员等级（0表示适用于所有等级）")
    private Integer memberLevel;

    @NotNull
    @Min(value = 1)
    @Max(value = 10)
    @Schema(description = "推荐层级（1-直推，2-二级，等）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer referralLevel;

    @NotNull
    @Min(value = 1)
    @Max(value = 3)
    @Schema(description = "奖励类型：1-固定金额 2-比例 3-阶梯式", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer rewardType;

    @NotNull
    @DecimalMin(value = "0")
    @Schema(description = "奖励金额/比例（单位：分或千分比）", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal rewardValue;

    @DecimalMin(value = "0")
    @Schema(description = "最小奖励金额（单位：分）")
    private BigDecimal minReward;

    @DecimalMin(value = "0")
    @Schema(description = "最大奖励金额（单位：分）")
    private BigDecimal maxReward;

    @Min(value = 0)
    @Max(value = 1)
    @Schema(description = "配置状态：0-禁用 1-启用")
    private Integer status;

    @NotNull
    @Schema(description = "生效时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant effectiveTime;

    @Schema(description = "失效时间")
    private Instant expireTime;

    @Size(max = 500)
    @Schema(description = "配置说明")
    private String description;

    @Schema(description = "逻辑删除标记")
    private Boolean deleted;

    @NotNull
    @Schema(description = "记录创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createTime;

    @Schema(description = "记录更新时间")
    private Instant updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public Integer getMemberLevel() {
        return memberLevel;
    }

    public void setMemberLevel(Integer memberLevel) {
        this.memberLevel = memberLevel;
    }

    public Integer getReferralLevel() {
        return referralLevel;
    }

    public void setReferralLevel(Integer referralLevel) {
        this.referralLevel = referralLevel;
    }

    public Integer getRewardType() {
        return rewardType;
    }

    public void setRewardType(Integer rewardType) {
        this.rewardType = rewardType;
    }

    public BigDecimal getRewardValue() {
        return rewardValue;
    }

    public void setRewardValue(BigDecimal rewardValue) {
        this.rewardValue = rewardValue;
    }

    public BigDecimal getMinReward() {
        return minReward;
    }

    public void setMinReward(BigDecimal minReward) {
        this.minReward = minReward;
    }

    public BigDecimal getMaxReward() {
        return maxReward;
    }

    public void setMaxReward(BigDecimal maxReward) {
        this.maxReward = maxReward;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Instant getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Instant effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Instant getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Instant getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public Instant getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Instant updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ReferralRewardConfigDTO)) {
            return false;
        }

        ReferralRewardConfigDTO referralRewardConfigDTO = (ReferralRewardConfigDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, referralRewardConfigDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ReferralRewardConfigDTO{" +
            "id=" + getId() +
            ", uuid='" + getUuid() + "'" +
            ", configName='" + getConfigName() + "'" +
            ", memberLevel=" + getMemberLevel() +
            ", referralLevel=" + getReferralLevel() +
            ", rewardType=" + getRewardType() +
            ", rewardValue=" + getRewardValue() +
            ", minReward=" + getMinReward() +
            ", maxReward=" + getMaxReward() +
            ", status=" + getStatus() +
            ", effectiveTime='" + getEffectiveTime() + "'" +
            ", expireTime='" + getExpireTime() + "'" +
            ", description='" + getDescription() + "'" +
            ", deleted='" + getDeleted() + "'" +
            ", createTime='" + getCreateTime() + "'" +
            ", updateTime='" + getUpdateTime() + "'" +
            "}";
    }
}
