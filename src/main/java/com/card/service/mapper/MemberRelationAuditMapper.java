package com.card.service.mapper;

import com.card.domain.MemberRelationAudit;
import com.card.domain.MemberRelations;
import com.card.service.dto.MemberRelationAuditDTO;
import com.card.service.dto.MemberRelationsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link MemberRelationAudit} and its DTO {@link MemberRelationAuditDTO}.
 */
@Mapper(componentModel = "spring")
public interface MemberRelationAuditMapper extends EntityMapper<MemberRelationAuditDTO, MemberRelationAudit> {
    @Mapping(target = "relation", source = "relation", qualifiedByName = "memberRelationsId")
    MemberRelationAuditDTO toDto(MemberRelationAudit s);

    @Named("memberRelationsId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    MemberRelationsDTO toDtoMemberRelationsId(MemberRelations memberRelations);
}
