package com.card.service.mapper;

import com.card.domain.Member;
import com.card.domain.MemberRelations;
import com.card.service.dto.MemberDTO;
import com.card.service.dto.MemberRelationsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link MemberRelations} and its DTO {@link MemberRelationsDTO}.
 */
@Mapper(componentModel = "spring")
public interface MemberRelationsMapper extends EntityMapper<MemberRelationsDTO, MemberRelations> {
    @Mapping(target = "member", source = "member", qualifiedByName = "memberId")
    @Mapping(target = "parent", source = "parent", qualifiedByName = "memberId")
    MemberRelationsDTO toDto(MemberRelations s);

    @Named("memberId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    MemberDTO toDtoMemberId(Member member);
}
