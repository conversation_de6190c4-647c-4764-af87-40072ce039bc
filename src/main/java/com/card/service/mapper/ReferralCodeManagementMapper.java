package com.card.service.mapper;

import com.card.domain.Member;
import com.card.domain.ReferralCodeManagement;
import com.card.service.dto.MemberDTO;
import com.card.service.dto.ReferralCodeManagementDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link ReferralCodeManagement} and its DTO {@link ReferralCodeManagementDTO}.
 */
@Mapper(componentModel = "spring")
public interface ReferralCodeManagementMapper extends EntityMapper<ReferralCodeManagementDTO, ReferralCodeManagement> {
    @Mapping(target = "member", source = "member", qualifiedByName = "memberId")
    ReferralCodeManagementDTO toDto(ReferralCodeManagement s);

    @Named("memberId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    MemberDTO toDtoMemberId(Member member);
}
