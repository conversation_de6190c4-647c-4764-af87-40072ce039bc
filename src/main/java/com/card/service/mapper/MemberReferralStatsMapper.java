package com.card.service.mapper;

import com.card.domain.Member;
import com.card.domain.MemberReferralStats;
import com.card.service.dto.MemberDTO;
import com.card.service.dto.MemberReferralStatsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link MemberReferralStats} and its DTO {@link MemberReferralStatsDTO}.
 */
@Mapper(componentModel = "spring")
public interface MemberReferralStatsMapper extends EntityMapper<MemberReferralStatsDTO, MemberReferralStats> {
    @Mapping(target = "member", source = "member", qualifiedByName = "memberId")
    MemberReferralStatsDTO toDto(MemberReferralStats s);

    @Named("memberId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    MemberDTO toDtoMemberId(Member member);
}
