package com.card.service;

import com.card.domain.ReferralCodeManagement;
import com.card.repository.ReferralCodeManagementRepository;
import com.card.service.dto.ReferralCodeManagementDTO;
import com.card.service.mapper.ReferralCodeManagementMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 管理推荐码的服务实现。
 * Service Implementation for managing {@link com.card.domain.ReferralCodeManagement}.
 */
@Service
@Transactional
public class ReferralCodeManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(ReferralCodeManagementService.class);

    private final ReferralCodeManagementRepository referralCodeManagementRepository;

    private final ReferralCodeManagementMapper referralCodeManagementMapper;

    public ReferralCodeManagementService(
        ReferralCodeManagementRepository referralCodeManagementRepository,
        ReferralCodeManagementMapper referralCodeManagementMapper
    ) {
        this.referralCodeManagementRepository = referralCodeManagementRepository;
        this.referralCodeManagementMapper = referralCodeManagementMapper;
    }

    /**
     * 保存推荐码信息。
     * Save a referralCodeManagement.
     *
     * @param referralCodeManagementDTO 要保存的实体
     * @return 已持久化的实体
     */
    public ReferralCodeManagementDTO save(ReferralCodeManagementDTO referralCodeManagementDTO) {
        LOG.debug("Request to save ReferralCodeManagement : {}", referralCodeManagementDTO);
        ReferralCodeManagement referralCodeManagement = referralCodeManagementMapper.toEntity(referralCodeManagementDTO);
        referralCodeManagement = referralCodeManagementRepository.save(referralCodeManagement);
        return referralCodeManagementMapper.toDto(referralCodeManagement);
    }

    /**
     * 更新推荐码信息。
     * Update a referralCodeManagement.
     *
     * @param referralCodeManagementDTO 要保存的实体
     * @return 已持久化的实体
     */
    public ReferralCodeManagementDTO update(ReferralCodeManagementDTO referralCodeManagementDTO) {
        LOG.debug("Request to update ReferralCodeManagement : {}", referralCodeManagementDTO);
        ReferralCodeManagement referralCodeManagement = referralCodeManagementMapper.toEntity(referralCodeManagementDTO);
        referralCodeManagement = referralCodeManagementRepository.save(referralCodeManagement);
        return referralCodeManagementMapper.toDto(referralCodeManagement);
    }

    /**
     * 部分更新推荐码信息。
     * Partially update a referralCodeManagement.
     *
     * @param referralCodeManagementDTO 要部分更新的实体
     * @return 已持久化的实体
     */
    public Optional<ReferralCodeManagementDTO> partialUpdate(ReferralCodeManagementDTO referralCodeManagementDTO) {
        LOG.debug("Request to partially update ReferralCodeManagement : {}", referralCodeManagementDTO);

        return referralCodeManagementRepository
            .findById(referralCodeManagementDTO.getId())
            .map(existingReferralCodeManagement -> {
                referralCodeManagementMapper.partialUpdate(existingReferralCodeManagement, referralCodeManagementDTO);

                return existingReferralCodeManagement;
            })
            .map(referralCodeManagementRepository::save)
            .map(referralCodeManagementMapper::toDto);
    }

    /**
     * 获取所有推荐码信息。
     * Get all the referralCodeManagements.
     *
     * @param pageable 分页信息
     * @return 实体列表
     */
    @Transactional(readOnly = true)
    public Page<ReferralCodeManagementDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all ReferralCodeManagements");
        return referralCodeManagementRepository.findAll(pageable).map(referralCodeManagementMapper::toDto);
    }

    /**
     * 根据ID获取推荐码信息。
     * Get one referralCodeManagement by id.
     *
     * @param id 实体的ID
     * @return 实体
     */
    @Transactional(readOnly = true)
    public Optional<ReferralCodeManagementDTO> findOne(Long id) {
        LOG.debug("Request to get ReferralCodeManagement : {}", id);
        return referralCodeManagementRepository.findById(id).map(referralCodeManagementMapper::toDto);
    }

    /**
     * 根据ID删除推荐码信息。
     * Delete the referralCodeManagement by id.
     *
     * @param id 实体的ID
     */
    public void delete(Long id) {
        LOG.debug("Request to delete ReferralCodeManagement : {}", id);
        referralCodeManagementRepository.deleteById(id);
    }
}
