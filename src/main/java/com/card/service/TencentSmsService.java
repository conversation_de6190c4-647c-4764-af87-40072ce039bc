package com.card.service;

/**
 * 腾讯云短信服务接口
 * Tencent Cloud SMS Service Interface
 */
public interface TencentSmsService {
    /**
     * 发送验证码短信
     * Send verification code SMS
     *
     * @param phone 手机号码
     * @param code 验证码
     * @return 发送是否成功
     */
    boolean sendVerificationCode(String phone, String code);

    /**
     * 发送自定义短信
     * Send custom SMS
     *
     * @param phone 手机号码
     * @param templateId 模板ID
     * @param params 模板参数
     * @return 发送是否成功
     */
    boolean sendSms(String phone, String templateId, String[] params);

    /**
     * 批量发送短信
     * Send SMS in batch
     *
     * @param phones 手机号码列表
     * @param templateId 模板ID
     * @param params 模板参数
     * @return 发送是否成功
     */
    boolean sendBatchSms(String[] phones, String templateId, String[] params);
}
