package com.card.service.factory;

import com.card.config.FileUploadProperties;
import com.card.service.FileUploadService;
import com.card.service.impl.AliyunOSSUploadServiceImpl;
import com.card.service.impl.TencentCOSUploadServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 文件上传服务工厂类
 */
@Component
public class FileUploadServiceFactory {

    private static final Logger LOG = LoggerFactory.getLogger(FileUploadServiceFactory.class);

    private final FileUploadProperties fileUploadProperties;
    private final ApplicationContext applicationContext;

    public FileUploadServiceFactory(FileUploadProperties fileUploadProperties, ApplicationContext applicationContext) {
        this.fileUploadProperties = fileUploadProperties;
        this.applicationContext = applicationContext;
    }

    /**
     * 获取文件上传服务实例
     *
     * @return 文件上传服务
     */
    public FileUploadService getFileUploadService() {
        String provider = fileUploadProperties.getProvider();

        LOG.debug("使用文件上传提供商: {}", provider);

        switch (provider.toLowerCase()) {
            case "tencent":
                return applicationContext.getBean("tencentCOSUploadService", TencentCOSUploadServiceImpl.class);
            case "aliyun":
                return applicationContext.getBean("aliyunOSSUploadService", AliyunOSSUploadServiceImpl.class);
            default:
                LOG.warn("未知的文件上传提供商: {}，默认使用腾讯云COS", provider);
                return applicationContext.getBean("tencentCOSUploadService", TencentCOSUploadServiceImpl.class);
        }
    }

    /**
     * 获取指定提供商的文件上传服务
     *
     * @param provider 提供商名称 (tencent/aliyun)
     * @return 文件上传服务
     */
    public FileUploadService getFileUploadService(String provider) {
        LOG.debug("指定使用文件上传提供商: {}", provider);

        switch (provider.toLowerCase()) {
            case "tencent":
                return applicationContext.getBean("tencentCOSUploadService", TencentCOSUploadServiceImpl.class);
            case "aliyun":
                return applicationContext.getBean("aliyunOSSUploadService", AliyunOSSUploadServiceImpl.class);
            default:
                throw new IllegalArgumentException("不支持的文件上传提供商: " + provider);
        }
    }
}
