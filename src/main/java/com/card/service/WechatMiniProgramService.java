package com.card.service;

import com.card.config.ApplicationProperties;
import com.card.utils.WechatCryptoUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * 微信小程序服务
 * WeChat Mini Program Service
 */
@Service
public class WechatMiniProgramService {

    private static final Logger LOG = LoggerFactory.getLogger(WechatMiniProgramService.class);

    private final ApplicationProperties.Wechat.MiniProgram wechatProperties;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public WechatMiniProgramService(ApplicationProperties applicationProperties) {
        this.wechatProperties = applicationProperties.getWechat().getMiniProgram();
        this.objectMapper = new ObjectMapper();
        this.webClient = WebClient.builder().baseUrl(wechatProperties.getApiBaseUrl()).build();
    }

    /**
     * 通过授权码获取session信息
     * Get session info by authorization code
     *
     * @param code 微信授权码
     * @return session信息
     */
    public WechatSessionInfo getSessionInfo(String code) {
        if (!StringUtils.hasText(code)) {
            LOG.warn("微信授权码为空");
            return null;
        }

        if (!isWechatConfigValid()) {
            LOG.error("微信小程序配置不完整");
            return null;
        }

        try {
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("appid", wechatProperties.getAppId());
            params.put("secret", wechatProperties.getAppSecret());
            params.put("js_code", code);
            params.put("grant_type", wechatProperties.getGrantType());

            // 发送请求
            String response = webClient
                .get()
                .uri(uriBuilder -> {
                    uriBuilder.path(wechatProperties.getSessionApiPath());
                    params.forEach(uriBuilder::queryParam);
                    return uriBuilder.build();
                })
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMillis(wechatProperties.getReadTimeout()))
                .block();

            if (!StringUtils.hasText(response)) {
                LOG.error("微信API响应为空");
                return null;
            }

            LOG.debug("微信API响应: {}", response);

            // 解析响应
            JsonNode jsonNode = objectMapper.readTree(response);

            // 检查是否有错误
            if (jsonNode.has("errcode")) {
                int errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.has("errmsg") ? jsonNode.get("errmsg").asText() : "未知错误";
                LOG.error("微信API返回错误: errcode={}, errmsg={}", errcode, errmsg);
                return null;
            }

            // 构建session信息
            WechatSessionInfo sessionInfo = new WechatSessionInfo();
            sessionInfo.setOpenid(getJsonString(jsonNode, "openid"));
            sessionInfo.setSessionKey(getJsonString(jsonNode, "session_key"));
            sessionInfo.setUnionid(getJsonString(jsonNode, "unionid"));

            LOG.info("获取微信session信息成功: openid={}", sessionInfo.getOpenid());
            return sessionInfo;
        } catch (Exception e) {
            LOG.error("获取微信session信息失败: code={}", code, e);
            return null;
        }
    }

    /**
     * 解密微信用户信息
     * Decrypt WeChat user info
     *
     * @param sessionKey 会话密钥
     * @param encryptedData 加密数据
     * @param iv 初始向量
     * @return 用户信息
     */
    public WechatCryptoUtils.WechatUserInfo decryptUserInfo(String sessionKey, String encryptedData, String iv) {
        if (!StringUtils.hasText(sessionKey) || !StringUtils.hasText(encryptedData) || !StringUtils.hasText(iv)) {
            LOG.warn("解密微信用户信息参数不完整");
            return null;
        }

        try {
            WechatCryptoUtils.WechatUserInfo userInfo = WechatCryptoUtils.decryptUserInfo(encryptedData, sessionKey, iv);
            if (userInfo != null) {
                LOG.debug("解密微信用户信息成功: openId={}", userInfo.getOpenId());
            } else {
                LOG.warn("解密微信用户信息失败");
            }
            return userInfo;
        } catch (Exception e) {
            LOG.error("解密微信用户信息异常", e);
            return null;
        }
    }

    /**
     * 解密微信手机号信息
     * Decrypt WeChat phone info
     *
     * @param sessionKey 会话密钥
     * @param encryptedData 加密数据
     * @param iv 初始向量
     * @return 手机号信息
     */
    public WechatCryptoUtils.WechatPhoneInfo decryptPhoneInfo(String sessionKey, String encryptedData, String iv) {
        if (!StringUtils.hasText(sessionKey) || !StringUtils.hasText(encryptedData) || !StringUtils.hasText(iv)) {
            LOG.warn("解密微信手机号信息参数不完整");
            return null;
        }

        try {
            WechatCryptoUtils.WechatPhoneInfo phoneInfo = WechatCryptoUtils.decryptPhoneInfo(encryptedData, sessionKey, iv);
            if (phoneInfo != null) {
                LOG.debug("解密微信手机号信息成功: phoneNumber={}", phoneInfo.getPhoneNumber());
            } else {
                LOG.warn("解密微信手机号信息失败");
            }
            return phoneInfo;
        } catch (Exception e) {
            LOG.error("解密微信手机号信息异常", e);
            return null;
        }
    }

    /**
     * 从JSON节点获取字符串值
     */
    private String getJsonString(JsonNode jsonNode, String fieldName) {
        JsonNode field = jsonNode.get(fieldName);
        return field != null && !field.isNull() ? field.asText() : null;
    }

    /**
     * 验证微信配置是否有效
     */
    private boolean isWechatConfigValid() {
        return (
            wechatProperties.getAppId() != null &&
            !wechatProperties.getAppId().trim().isEmpty() &&
            wechatProperties.getAppSecret() != null &&
            !wechatProperties.getAppSecret().trim().isEmpty()
        );
    }

    /**
     * 微信Session信息
     */
    public static class WechatSessionInfo {

        private String openid;
        private String sessionKey;
        private String unionid;

        // Getter和Setter方法
        public String getOpenid() {
            return openid;
        }

        public void setOpenid(String openid) {
            this.openid = openid;
        }

        public String getSessionKey() {
            return sessionKey;
        }

        public void setSessionKey(String sessionKey) {
            this.sessionKey = sessionKey;
        }

        public String getUnionid() {
            return unionid;
        }

        public void setUnionid(String unionid) {
            this.unionid = unionid;
        }

        /**
         * 检查session信息是否有效
         */
        public boolean isValid() {
            return StringUtils.hasText(openid) && StringUtils.hasText(sessionKey);
        }

        @Override
        public String toString() {
            return (
                "WechatSessionInfo{" +
                "openid='" +
                openid +
                '\'' +
                ", sessionKey='" +
                (sessionKey != null ? "***" : null) +
                '\'' +
                ", unionid='" +
                unionid +
                '\'' +
                '}'
            );
        }
    }
}
