package com.card.service;

import java.util.Map;

/**
 * 验证码服务接口
 * Verification Code Service Interface
 */
public interface VerificationCodeServiceInterface {
    /**
     * 发送验证码
     *
     * @param phone 手机号
     * @param type 验证码类型
     * @return 发送结果
     */
    Map<String, Object> sendVerificationCode(String phone, String type);

    /**
     * 验证验证码
     *
     * @param phone 手机号
     * @param type 验证码类型
     * @param code 验证码
     * @return 验证是否成功
     */
    boolean verifyCode(String phone, String type, String code);

    /**
     * 清除验证码
     *
     * @param phone 手机号
     * @param type 验证码类型
     */
    void clearVerificationCode(String phone, String type);

    /**
     * 检查是否可以发送验证码
     *
     * @param phone 手机号
     * @return 检查结果
     */
    Map<String, Object> checkSendLimit(String phone);
}
