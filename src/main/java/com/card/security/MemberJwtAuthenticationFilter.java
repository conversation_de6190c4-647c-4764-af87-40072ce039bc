package com.card.security;

import com.card.domain.Member;
import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * 会员JWT认证过滤器
 * Member JWT Authentication Filter
 *
 * 用于处理前端会员的JWT Token认证，将认证信息设置到Spring Security上下文中
 */
@Component
public class MemberJwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger LOG = LoggerFactory.getLogger(MemberJwtAuthenticationFilter.class);

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
        throws ServletException, IOException {
        try {
            // 只处理会员相关的API请求
            String requestPath = request.getRequestURI();
            if (!shouldProcessRequest(requestPath)) {
                filterChain.doFilter(request, response);
                return;
            }

            // 提取JWT Token
            Optional<String> tokenOpt = MemberSecurityUtils.extractTokenFromRequest(request);
            if (tokenOpt.isEmpty()) {
                LOG.debug("请求中未找到Member JWT Token: {}", requestPath);
                filterChain.doFilter(request, response);
                return;
            }

            String token = tokenOpt.orElseThrow();

            // 验证Token有效性
            if (!MemberSecurityUtils.validateMemberToken(token)) {
                LOG.debug("Member JWT Token验证失败: {}", requestPath);
                filterChain.doFilter(request, response);
                return;
            }

            // 解析Token获取会员信息
            Claims claims = MemberSecurityUtils.parseMemberJwtToken(token);
            if (claims == null) {
                LOG.debug("Member JWT Token解析失败: {}", requestPath);
                filterChain.doFilter(request, response);
                return;
            }

            // 获取会员UUID（Token的subject）
            String memberUuid = claims.getSubject();
            if (!StringUtils.hasText(memberUuid)) {
                LOG.debug("Member JWT Token中未找到有效的subject: {}", requestPath);
                filterChain.doFilter(request, response);
                return;
            }

            // 验证会员是否存在且状态正常
            Optional<Member> memberOpt = MemberSecurityUtils.getCurrentMemberFromJwt(token);
            if (memberOpt.isEmpty()) {
                LOG.debug("根据JWT Token未找到有效会员: uuid={}", memberUuid);
                filterChain.doFilter(request, response);
                return;
            }

            Member member = memberOpt.orElseThrow();

            // 检查会员状态
            if (member.getStatus() == null || member.getStatus() != 1) {
                LOG.debug("会员状态异常，拒绝认证: uuid={}, status={}", memberUuid, member.getStatus());
                filterChain.doFilter(request, response);
                return;
            }

            // 创建认证对象并设置到Spring Security上下文
            setAuthenticationInContext(member, request);

            LOG.debug("Member JWT认证成功: uuid={}, phone={}", member.getUuid(), member.getPhone());
        } catch (Exception e) {
            LOG.error("Member JWT认证过滤器处理异常", e);
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 判断是否需要处理该请求
     *
     * @param requestPath 请求路径
     * @return 是否需要处理
     */
    private boolean shouldProcessRequest(String requestPath) {
        // 处理会员相关的API请求、认证请求和名片请求
        return (
            (requestPath.startsWith("/api/members/") && !isPublicMemberEndpoint(requestPath)) ||
            (requestPath.startsWith("/api/auth/members/") && !isPublicAuthEndpoint(requestPath)) ||
            requestPath.startsWith("/api/cards/") ||
            requestPath.startsWith("/api/contacts/") ||
            requestPath.startsWith("/api/polygon/") ||
            requestPath.startsWith("/api/wallets/") ||
            requestPath.startsWith("/api/wallet-securities") ||
            requestPath.startsWith("/api/wallet-transactions")
        );
    }

    /**
     * 判断是否是公开的会员端点（不需要认证）
     *
     * @param requestPath 请求路径
     * @return 是否是公开端点
     */
    private boolean isPublicMemberEndpoint(String requestPath) {
        // 这些端点不需要认证
        return (
            requestPath.equals("/api/members/register-by-phone") ||
            requestPath.equals("/api/members/register-by-wechat") ||
            requestPath.equals("/api/members/send-register-code") ||
            requestPath.equals("/api/members/check-phone-available") ||
            requestPath.equals("/api/members/check-send-limit") ||
            requestPath.equals("/api/members/test/public")
        );
    }

    /**
     * 判断是否是公开的认证端点（不需要认证）
     *
     * @param requestPath 请求路径
     * @return 是否是公开端点
     */
    private boolean isPublicAuthEndpoint(String requestPath) {
        // 这些认证端点不需要预先认证
        return (
            requestPath.equals("/api/auth/members/authenticate") ||
            requestPath.equals("/api/auth/members/validate-token") ||
            requestPath.equals("/api/auth/members/refresh-token") ||
            requestPath.equals("/api/auth/members/logout")
        );
    }

    /**
     * 设置认证信息到Spring Security上下文
     *
     * @param member 会员对象
     * @param request HTTP请求
     */
    private void setAuthenticationInContext(Member member, HttpServletRequest request) {
        // 创建权限列表
        SimpleGrantedAuthority authority = new SimpleGrantedAuthority("ROLE_MEMBER");

        // 创建认证对象，principal使用member的UUID
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
            member.getUuid(), // principal
            null, // credentials
            Collections.singletonList(authority) // authorities
        );

        // 设置认证详情
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

        // 设置到Spring Security上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }
}
