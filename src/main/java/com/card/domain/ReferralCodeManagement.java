package com.card.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 推荐码管理实体
 * 管理推荐码的生成、使用、过期等
 */
@Entity
@Table(name = "referral_code_management")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ReferralCodeManagement implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 业务唯一标识
     */
    @NotNull
    @Column(name = "uuid", nullable = false, unique = true)
    private String uuid;

    /**
     * 推荐码
     */
    @NotNull
    @Size(max = 10)
    @Column(name = "referral_code", length = 10, nullable = false, unique = true)
    private String referralCode;

    /**
     * 推荐码类型：1-个人专属 2-临时活动 3-批量生成
     */
    @NotNull
    @Min(value = 1)
    @Max(value = 3)
    @Column(name = "code_type", nullable = false)
    private Integer codeType;

    /**
     * 使用次数限制（0表示无限制）
     */
    @Min(value = 0)
    @Column(name = "usage_limit")
    private Integer usageLimit;

    /**
     * 已使用次数
     */
    @Min(value = 0)
    @Column(name = "used_count")
    private Integer usedCount;

    /**
     * 推荐码状态：0-禁用 1-启用 2-过期
     */
    @Min(value = 0)
    @Max(value = 2)
    @Column(name = "status")
    private Integer status;

    /**
     * 生效时间
     */
    @NotNull
    @Column(name = "effective_time", nullable = false)
    private Instant effectiveTime;

    /**
     * 过期时间
     */
    @Column(name = "expire_time")
    private Instant expireTime;

    /**
     * 推荐码说明
     */
    @Size(max = 255)
    @Column(name = "description", length = 255)
    private String description;

    /**
     * 逻辑删除标记
     */
    @Column(name = "deleted")
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    @NotNull
    @Column(name = "create_time", nullable = false)
    private Instant createTime;

    /**
     * 记录更新时间
     */
    @Column(name = "update_time")
    private Instant updateTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "member_id")
    private Member member;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public ReferralCodeManagement id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return this.uuid;
    }

    public ReferralCodeManagement uuid(String uuid) {
        this.setUuid(uuid);
        return this;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getReferralCode() {
        return this.referralCode;
    }

    public ReferralCodeManagement referralCode(String referralCode) {
        this.setReferralCode(referralCode);
        return this;
    }

    public void setReferralCode(String referralCode) {
        this.referralCode = referralCode;
    }

    public Integer getCodeType() {
        return this.codeType;
    }

    public ReferralCodeManagement codeType(Integer codeType) {
        this.setCodeType(codeType);
        return this;
    }

    public void setCodeType(Integer codeType) {
        this.codeType = codeType;
    }

    public Integer getUsageLimit() {
        return this.usageLimit;
    }

    public ReferralCodeManagement usageLimit(Integer usageLimit) {
        this.setUsageLimit(usageLimit);
        return this;
    }

    public void setUsageLimit(Integer usageLimit) {
        this.usageLimit = usageLimit;
    }

    public Integer getUsedCount() {
        return this.usedCount;
    }

    public ReferralCodeManagement usedCount(Integer usedCount) {
        this.setUsedCount(usedCount);
        return this;
    }

    public void setUsedCount(Integer usedCount) {
        this.usedCount = usedCount;
    }

    public Integer getStatus() {
        return this.status;
    }

    public ReferralCodeManagement status(Integer status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Instant getEffectiveTime() {
        return this.effectiveTime;
    }

    public ReferralCodeManagement effectiveTime(Instant effectiveTime) {
        this.setEffectiveTime(effectiveTime);
        return this;
    }

    public void setEffectiveTime(Instant effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Instant getExpireTime() {
        return this.expireTime;
    }

    public ReferralCodeManagement expireTime(Instant expireTime) {
        this.setExpireTime(expireTime);
        return this;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public String getDescription() {
        return this.description;
    }

    public ReferralCodeManagement description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getDeleted() {
        return this.deleted;
    }

    public ReferralCodeManagement deleted(Boolean deleted) {
        this.setDeleted(deleted);
        return this;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Instant getCreateTime() {
        return this.createTime;
    }

    public ReferralCodeManagement createTime(Instant createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public Instant getUpdateTime() {
        return this.updateTime;
    }

    public ReferralCodeManagement updateTime(Instant updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Instant updateTime) {
        this.updateTime = updateTime;
    }

    public Member getMember() {
        return this.member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public ReferralCodeManagement member(Member member) {
        this.setMember(member);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ReferralCodeManagement)) {
            return false;
        }
        return getId() != null && getId().equals(((ReferralCodeManagement) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ReferralCodeManagement{" +
            "id=" + getId() +
            ", uuid='" + getUuid() + "'" +
            ", referralCode='" + getReferralCode() + "'" +
            ", codeType=" + getCodeType() +
            ", usageLimit=" + getUsageLimit() +
            ", usedCount=" + getUsedCount() +
            ", status=" + getStatus() +
            ", effectiveTime='" + getEffectiveTime() + "'" +
            ", expireTime='" + getExpireTime() + "'" +
            ", description='" + getDescription() + "'" +
            ", deleted='" + getDeleted() + "'" +
            ", createTime='" + getCreateTime() + "'" +
            ", updateTime='" + getUpdateTime() + "'" +
            "}";
    }
}
