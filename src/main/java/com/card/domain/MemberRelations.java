package com.card.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 会员关系实体 - 会员层级关系表
 * 存储会员推荐关系、团队结构等层级信息
 */
@Entity
@Table(name = "member_relations")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class MemberRelations implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 业务唯一标识（32位UUID，用于对外接口）
     */
    @NotNull
    @Column(name = "uuid", nullable = false, unique = true)
    private String uuid;

    /**
     * 推荐码（用于记录通过哪个推荐码建立的关系）
     */
    @Size(max = 10)
    @Column(name = "referral_code", length = 10)
    private String referralCode;

    /**
     * 关系类型：1-推荐人 2-团队长 3-代理商 4-合作伙伴
     */
    @NotNull
    @Min(value = 1)
    @Max(value = 4)
    @Column(name = "relation_type", nullable = false)
    private Integer relationType;

    /**
     * 层级深度（1-直接推荐，2-二级推荐，以此类推）
     */
    @Min(value = 1)
    @Max(value = 10)
    @Column(name = "level")
    private Integer level;

    /**
     * 完整路径（用/分隔的ID链，便于快速查询）
     */
    @Size(max = 1000)
    @Column(name = "relation_path", length = 1000)
    private String relationPath;

    /**
     * 关系状态：0-失效 1-有效 2-暂停 3-冻结
     */
    @Min(value = 0)
    @Max(value = 3)
    @Column(name = "status")
    private Integer status;

    /**
     * 推荐奖励金额（单位：分）
     */
    @DecimalMin(value = "0")
    @Column(name = "reward_amount", precision = 21, scale = 2)
    private BigDecimal rewardAmount;

    /**
     * 奖励状态：0-未发放 1-已发放 2-已取消 3-计算中
     */
    @Min(value = 0)
    @Max(value = 3)
    @Column(name = "reward_status")
    private Integer rewardStatus;

    /**
     * 奖励发放时间
     */
    @Column(name = "reward_time")
    private Instant rewardTime;

    /**
     * 关系生效时间
     */
    @NotNull
    @Column(name = "effective_time", nullable = false)
    private Instant effectiveTime;

    /**
     * 关系失效时间
     */
    @Column(name = "expire_time")
    private Instant expireTime;

    /**
     * 冻结原因
     */
    @Size(max = 255)
    @Column(name = "frozen_reason", length = 255)
    private String frozenReason;

    /**
     * 冻结时间
     */
    @Column(name = "frozen_time")
    private Instant frozenTime;

    /**
     * 解冻时间
     */
    @Column(name = "unfrozen_time")
    private Instant unfrozenTime;

    /**
     * 备注说明
     */
    @Size(max = 500)
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 数据来源：1-系统自动 2-手动录入 3-批量导入
     */
    @Min(value = 1)
    @Max(value = 3)
    @Column(name = "data_source")
    private Integer dataSource;

    /**
     * 操作人员ID
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 逻辑删除标记：false-正常 true-已删除
     */
    @Column(name = "deleted")
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    @NotNull
    @Column(name = "create_time", nullable = false)
    private Instant createTime;

    /**
     * 记录更新时间
     */
    @Column(name = "update_time")
    private Instant updateTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "member_id")
    private Member member;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    private Member parent;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public MemberRelations id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return this.uuid;
    }

    public MemberRelations uuid(String uuid) {
        this.setUuid(uuid);
        return this;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getReferralCode() {
        return this.referralCode;
    }

    public MemberRelations referralCode(String referralCode) {
        this.setReferralCode(referralCode);
        return this;
    }

    public void setReferralCode(String referralCode) {
        this.referralCode = referralCode;
    }

    public Integer getRelationType() {
        return this.relationType;
    }

    public MemberRelations relationType(Integer relationType) {
        this.setRelationType(relationType);
        return this;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    public Integer getLevel() {
        return this.level;
    }

    public MemberRelations level(Integer level) {
        this.setLevel(level);
        return this;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getRelationPath() {
        return this.relationPath;
    }

    public MemberRelations relationPath(String relationPath) {
        this.setRelationPath(relationPath);
        return this;
    }

    public void setRelationPath(String relationPath) {
        this.relationPath = relationPath;
    }

    public Integer getStatus() {
        return this.status;
    }

    public MemberRelations status(Integer status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getRewardAmount() {
        return this.rewardAmount;
    }

    public MemberRelations rewardAmount(BigDecimal rewardAmount) {
        this.setRewardAmount(rewardAmount);
        return this;
    }

    public void setRewardAmount(BigDecimal rewardAmount) {
        this.rewardAmount = rewardAmount;
    }

    public Integer getRewardStatus() {
        return this.rewardStatus;
    }

    public MemberRelations rewardStatus(Integer rewardStatus) {
        this.setRewardStatus(rewardStatus);
        return this;
    }

    public void setRewardStatus(Integer rewardStatus) {
        this.rewardStatus = rewardStatus;
    }

    public Instant getRewardTime() {
        return this.rewardTime;
    }

    public MemberRelations rewardTime(Instant rewardTime) {
        this.setRewardTime(rewardTime);
        return this;
    }

    public void setRewardTime(Instant rewardTime) {
        this.rewardTime = rewardTime;
    }

    public Instant getEffectiveTime() {
        return this.effectiveTime;
    }

    public MemberRelations effectiveTime(Instant effectiveTime) {
        this.setEffectiveTime(effectiveTime);
        return this;
    }

    public void setEffectiveTime(Instant effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Instant getExpireTime() {
        return this.expireTime;
    }

    public MemberRelations expireTime(Instant expireTime) {
        this.setExpireTime(expireTime);
        return this;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public String getFrozenReason() {
        return this.frozenReason;
    }

    public MemberRelations frozenReason(String frozenReason) {
        this.setFrozenReason(frozenReason);
        return this;
    }

    public void setFrozenReason(String frozenReason) {
        this.frozenReason = frozenReason;
    }

    public Instant getFrozenTime() {
        return this.frozenTime;
    }

    public MemberRelations frozenTime(Instant frozenTime) {
        this.setFrozenTime(frozenTime);
        return this;
    }

    public void setFrozenTime(Instant frozenTime) {
        this.frozenTime = frozenTime;
    }

    public Instant getUnfrozenTime() {
        return this.unfrozenTime;
    }

    public MemberRelations unfrozenTime(Instant unfrozenTime) {
        this.setUnfrozenTime(unfrozenTime);
        return this;
    }

    public void setUnfrozenTime(Instant unfrozenTime) {
        this.unfrozenTime = unfrozenTime;
    }

    public String getRemark() {
        return this.remark;
    }

    public MemberRelations remark(String remark) {
        this.setRemark(remark);
        return this;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDataSource() {
        return this.dataSource;
    }

    public MemberRelations dataSource(Integer dataSource) {
        this.setDataSource(dataSource);
        return this;
    }

    public void setDataSource(Integer dataSource) {
        this.dataSource = dataSource;
    }

    public Long getOperatorId() {
        return this.operatorId;
    }

    public MemberRelations operatorId(Long operatorId) {
        this.setOperatorId(operatorId);
        return this;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Boolean getDeleted() {
        return this.deleted;
    }

    public MemberRelations deleted(Boolean deleted) {
        this.setDeleted(deleted);
        return this;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Instant getCreateTime() {
        return this.createTime;
    }

    public MemberRelations createTime(Instant createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public Instant getUpdateTime() {
        return this.updateTime;
    }

    public MemberRelations updateTime(Instant updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Instant updateTime) {
        this.updateTime = updateTime;
    }

    public Member getMember() {
        return this.member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public MemberRelations member(Member member) {
        this.setMember(member);
        return this;
    }

    public Member getParent() {
        return this.parent;
    }

    public void setParent(Member parent) {
        this.parent = parent;
    }

    public MemberRelations parent(Member parent) {
        this.setParent(parent);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MemberRelations)) {
            return false;
        }
        return getId() != null && getId().equals(((MemberRelations) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "MemberRelations{" +
            "id=" + getId() +
            ", uuid='" + getUuid() + "'" +
            ", referralCode='" + getReferralCode() + "'" +
            ", relationType=" + getRelationType() +
            ", level=" + getLevel() +
            ", relationPath='" + getRelationPath() + "'" +
            ", status=" + getStatus() +
            ", rewardAmount=" + getRewardAmount() +
            ", rewardStatus=" + getRewardStatus() +
            ", rewardTime='" + getRewardTime() + "'" +
            ", effectiveTime='" + getEffectiveTime() + "'" +
            ", expireTime='" + getExpireTime() + "'" +
            ", frozenReason='" + getFrozenReason() + "'" +
            ", frozenTime='" + getFrozenTime() + "'" +
            ", unfrozenTime='" + getUnfrozenTime() + "'" +
            ", remark='" + getRemark() + "'" +
            ", dataSource=" + getDataSource() +
            ", operatorId=" + getOperatorId() +
            ", deleted='" + getDeleted() + "'" +
            ", createTime='" + getCreateTime() + "'" +
            ", updateTime='" + getUpdateTime() + "'" +
            "}";
    }
}
