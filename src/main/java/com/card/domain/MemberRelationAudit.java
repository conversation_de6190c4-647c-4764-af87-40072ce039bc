package com.card.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 推荐关系审计日志
 * 记录推荐关系的所有变更操作
 */
@Entity
@Table(name = "member_relation_audit")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class MemberRelationAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 业务唯一标识
     */
    @NotNull
    @Column(name = "uuid", nullable = false, unique = true)
    private String uuid;

    /**
     * 操作类型：1-创建 2-更新 3-删除 4-冻结 5-解冻
     */
    @NotNull
    @Min(value = 1)
    @Max(value = 5)
    @Column(name = "operation_type", nullable = false)
    private Integer operationType;

    /**
     * 操作前数据（JSON格式）
     */
    @Lob
    @Column(name = "before_data")
    private String beforeData;

    /**
     * 操作后数据（JSON格式）
     */
    @Lob
    @Column(name = "after_data")
    private String afterData;

    /**
     * 操作原因
     */
    @Size(max = 500)
    @Column(name = "operation_reason", length = 500)
    private String operationReason;

    /**
     * 操作人员ID
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 操作人员姓名
     */
    @Size(max = 50)
    @Column(name = "operator_name", length = 50)
    private String operatorName;

    /**
     * 操作IP地址
     */
    @Size(max = 45)
    @Column(name = "operator_ip", length = 45)
    private String operatorIp;

    /**
     * 操作时间
     */
    @NotNull
    @Column(name = "operation_time", nullable = false)
    private Instant operationTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "relation_id")
    @JsonIgnoreProperties(value = { "member", "parent" }, allowSetters = true)
    private MemberRelations relation;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public MemberRelationAudit id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return this.uuid;
    }

    public MemberRelationAudit uuid(String uuid) {
        this.setUuid(uuid);
        return this;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getOperationType() {
        return this.operationType;
    }

    public MemberRelationAudit operationType(Integer operationType) {
        this.setOperationType(operationType);
        return this;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getBeforeData() {
        return this.beforeData;
    }

    public MemberRelationAudit beforeData(String beforeData) {
        this.setBeforeData(beforeData);
        return this;
    }

    public void setBeforeData(String beforeData) {
        this.beforeData = beforeData;
    }

    public String getAfterData() {
        return this.afterData;
    }

    public MemberRelationAudit afterData(String afterData) {
        this.setAfterData(afterData);
        return this;
    }

    public void setAfterData(String afterData) {
        this.afterData = afterData;
    }

    public String getOperationReason() {
        return this.operationReason;
    }

    public MemberRelationAudit operationReason(String operationReason) {
        this.setOperationReason(operationReason);
        return this;
    }

    public void setOperationReason(String operationReason) {
        this.operationReason = operationReason;
    }

    public Long getOperatorId() {
        return this.operatorId;
    }

    public MemberRelationAudit operatorId(Long operatorId) {
        this.setOperatorId(operatorId);
        return this;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return this.operatorName;
    }

    public MemberRelationAudit operatorName(String operatorName) {
        this.setOperatorName(operatorName);
        return this;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOperatorIp() {
        return this.operatorIp;
    }

    public MemberRelationAudit operatorIp(String operatorIp) {
        this.setOperatorIp(operatorIp);
        return this;
    }

    public void setOperatorIp(String operatorIp) {
        this.operatorIp = operatorIp;
    }

    public Instant getOperationTime() {
        return this.operationTime;
    }

    public MemberRelationAudit operationTime(Instant operationTime) {
        this.setOperationTime(operationTime);
        return this;
    }

    public void setOperationTime(Instant operationTime) {
        this.operationTime = operationTime;
    }

    public MemberRelations getRelation() {
        return this.relation;
    }

    public void setRelation(MemberRelations memberRelations) {
        this.relation = memberRelations;
    }

    public MemberRelationAudit relation(MemberRelations memberRelations) {
        this.setRelation(memberRelations);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MemberRelationAudit)) {
            return false;
        }
        return getId() != null && getId().equals(((MemberRelationAudit) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "MemberRelationAudit{" +
            "id=" + getId() +
            ", uuid='" + getUuid() + "'" +
            ", operationType=" + getOperationType() +
            ", beforeData='" + getBeforeData() + "'" +
            ", afterData='" + getAfterData() + "'" +
            ", operationReason='" + getOperationReason() + "'" +
            ", operatorId=" + getOperatorId() +
            ", operatorName='" + getOperatorName() + "'" +
            ", operatorIp='" + getOperatorIp() + "'" +
            ", operationTime='" + getOperationTime() + "'" +
            "}";
    }
}
