package com.card.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 推荐奖励配置实体
 * 存储不同层级、不同类型的奖励规则配置
 */
@Entity
@Table(name = "referral_reward_config")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ReferralRewardConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 业务唯一标识
     */
    @NotNull
    @Column(name = "uuid", nullable = false, unique = true)
    private String uuid;

    /**
     * 配置名称
     */
    @NotNull
    @Size(max = 100)
    @Column(name = "config_name", length = 100, nullable = false)
    private String configName;

    /**
     * 会员等级（0表示适用于所有等级）
     */
    @Min(value = 0)
    @Max(value = 10)
    @Column(name = "member_level")
    private Integer memberLevel;

    /**
     * 推荐层级（1-直推，2-二级，等）
     */
    @NotNull
    @Min(value = 1)
    @Max(value = 10)
    @Column(name = "referral_level", nullable = false)
    private Integer referralLevel;

    /**
     * 奖励类型：1-固定金额 2-比例 3-阶梯式
     */
    @NotNull
    @Min(value = 1)
    @Max(value = 3)
    @Column(name = "reward_type", nullable = false)
    private Integer rewardType;

    /**
     * 奖励金额/比例（单位：分或千分比）
     */
    @NotNull
    @DecimalMin(value = "0")
    @Column(name = "reward_value", precision = 21, scale = 2, nullable = false)
    private BigDecimal rewardValue;

    /**
     * 最小奖励金额（单位：分）
     */
    @DecimalMin(value = "0")
    @Column(name = "min_reward", precision = 21, scale = 2)
    private BigDecimal minReward;

    /**
     * 最大奖励金额（单位：分）
     */
    @DecimalMin(value = "0")
    @Column(name = "max_reward", precision = 21, scale = 2)
    private BigDecimal maxReward;

    /**
     * 配置状态：0-禁用 1-启用
     */
    @Min(value = 0)
    @Max(value = 1)
    @Column(name = "status")
    private Integer status;

    /**
     * 生效时间
     */
    @NotNull
    @Column(name = "effective_time", nullable = false)
    private Instant effectiveTime;

    /**
     * 失效时间
     */
    @Column(name = "expire_time")
    private Instant expireTime;

    /**
     * 配置说明
     */
    @Size(max = 500)
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 逻辑删除标记
     */
    @Column(name = "deleted")
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    @NotNull
    @Column(name = "create_time", nullable = false)
    private Instant createTime;

    /**
     * 记录更新时间
     */
    @Column(name = "update_time")
    private Instant updateTime;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public ReferralRewardConfig id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return this.uuid;
    }

    public ReferralRewardConfig uuid(String uuid) {
        this.setUuid(uuid);
        return this;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getConfigName() {
        return this.configName;
    }

    public ReferralRewardConfig configName(String configName) {
        this.setConfigName(configName);
        return this;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public Integer getMemberLevel() {
        return this.memberLevel;
    }

    public ReferralRewardConfig memberLevel(Integer memberLevel) {
        this.setMemberLevel(memberLevel);
        return this;
    }

    public void setMemberLevel(Integer memberLevel) {
        this.memberLevel = memberLevel;
    }

    public Integer getReferralLevel() {
        return this.referralLevel;
    }

    public ReferralRewardConfig referralLevel(Integer referralLevel) {
        this.setReferralLevel(referralLevel);
        return this;
    }

    public void setReferralLevel(Integer referralLevel) {
        this.referralLevel = referralLevel;
    }

    public Integer getRewardType() {
        return this.rewardType;
    }

    public ReferralRewardConfig rewardType(Integer rewardType) {
        this.setRewardType(rewardType);
        return this;
    }

    public void setRewardType(Integer rewardType) {
        this.rewardType = rewardType;
    }

    public BigDecimal getRewardValue() {
        return this.rewardValue;
    }

    public ReferralRewardConfig rewardValue(BigDecimal rewardValue) {
        this.setRewardValue(rewardValue);
        return this;
    }

    public void setRewardValue(BigDecimal rewardValue) {
        this.rewardValue = rewardValue;
    }

    public BigDecimal getMinReward() {
        return this.minReward;
    }

    public ReferralRewardConfig minReward(BigDecimal minReward) {
        this.setMinReward(minReward);
        return this;
    }

    public void setMinReward(BigDecimal minReward) {
        this.minReward = minReward;
    }

    public BigDecimal getMaxReward() {
        return this.maxReward;
    }

    public ReferralRewardConfig maxReward(BigDecimal maxReward) {
        this.setMaxReward(maxReward);
        return this;
    }

    public void setMaxReward(BigDecimal maxReward) {
        this.maxReward = maxReward;
    }

    public Integer getStatus() {
        return this.status;
    }

    public ReferralRewardConfig status(Integer status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Instant getEffectiveTime() {
        return this.effectiveTime;
    }

    public ReferralRewardConfig effectiveTime(Instant effectiveTime) {
        this.setEffectiveTime(effectiveTime);
        return this;
    }

    public void setEffectiveTime(Instant effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Instant getExpireTime() {
        return this.expireTime;
    }

    public ReferralRewardConfig expireTime(Instant expireTime) {
        this.setExpireTime(expireTime);
        return this;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public String getDescription() {
        return this.description;
    }

    public ReferralRewardConfig description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getDeleted() {
        return this.deleted;
    }

    public ReferralRewardConfig deleted(Boolean deleted) {
        this.setDeleted(deleted);
        return this;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Instant getCreateTime() {
        return this.createTime;
    }

    public ReferralRewardConfig createTime(Instant createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public Instant getUpdateTime() {
        return this.updateTime;
    }

    public ReferralRewardConfig updateTime(Instant updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Instant updateTime) {
        this.updateTime = updateTime;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ReferralRewardConfig)) {
            return false;
        }
        return getId() != null && getId().equals(((ReferralRewardConfig) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ReferralRewardConfig{" +
            "id=" + getId() +
            ", uuid='" + getUuid() + "'" +
            ", configName='" + getConfigName() + "'" +
            ", memberLevel=" + getMemberLevel() +
            ", referralLevel=" + getReferralLevel() +
            ", rewardType=" + getRewardType() +
            ", rewardValue=" + getRewardValue() +
            ", minReward=" + getMinReward() +
            ", maxReward=" + getMaxReward() +
            ", status=" + getStatus() +
            ", effectiveTime='" + getEffectiveTime() + "'" +
            ", expireTime='" + getExpireTime() + "'" +
            ", description='" + getDescription() + "'" +
            ", deleted='" + getDeleted() + "'" +
            ", createTime='" + getCreateTime() + "'" +
            ", updateTime='" + getUpdateTime() + "'" +
            "}";
    }
}
