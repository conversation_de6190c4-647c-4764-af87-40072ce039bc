package com.card.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 会员推荐统计实体 - 推荐业绩统计
 * 存储会员推荐数据的统计信息，便于快速查询
 */
@Entity
@Table(name = "member_referral_stats")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class MemberReferralStats implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 业务唯一标识（32位UUID）
     */
    @NotNull
    @Column(name = "uuid", nullable = false, unique = true)
    private String uuid;

    /**
     * 直接推荐人数
     */
    @Min(value = 0)
    @Column(name = "direct_referrals")
    private Integer directReferrals;

    /**
     * 间接推荐人数（团队总人数）
     */
    @Min(value = 0)
    @Column(name = "indirect_referrals")
    private Integer indirectReferrals;

    /**
     * 有效推荐人数（状态为有效的推荐）
     */
    @Min(value = 0)
    @Column(name = "active_referrals")
    private Integer activeReferrals;

    /**
     * 总推荐奖励金额（单位：分）
     */
    @DecimalMin(value = "0")
    @Column(name = "total_reward", precision = 21, scale = 2)
    private BigDecimal totalReward;

    /**
     * 已发放奖励金额
     */
    @DecimalMin(value = "0")
    @Column(name = "paid_reward", precision = 21, scale = 2)
    private BigDecimal paidReward;

    /**
     * 待发放奖励金额
     */
    @DecimalMin(value = "0")
    @Column(name = "pending_reward", precision = 21, scale = 2)
    private BigDecimal pendingReward;

    /**
     * 本月推荐人数
     */
    @Min(value = 0)
    @Column(name = "monthly_referrals")
    private Integer monthlyReferrals;

    /**
     * 本月推荐奖励（单位：分）
     */
    @DecimalMin(value = "0")
    @Column(name = "monthly_reward", precision = 21, scale = 2)
    private BigDecimal monthlyReward;

    /**
     * 本周推荐人数
     */
    @Min(value = 0)
    @Column(name = "weekly_referrals")
    private Integer weeklyReferrals;

    /**
     * 今日推荐人数
     */
    @Min(value = 0)
    @Column(name = "daily_referrals")
    private Integer dailyReferrals;

    /**
     * 统计月份（格式：YYYY-MM）
     */
    @NotNull
    @Size(max = 7)
    @Column(name = "stat_month", length = 7, nullable = false)
    private String statMonth;

    /**
     * 团队层级深度
     */
    @Min(value = 0)
    @Column(name = "max_team_level")
    private Integer maxTeamLevel;

    /**
     * 逻辑删除标记：false-正常 true-已删除
     */
    @Column(name = "deleted")
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    @NotNull
    @Column(name = "create_time", nullable = false)
    private Instant createTime;

    /**
     * 记录更新时间
     */
    @Column(name = "update_time")
    private Instant updateTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "member_id")
    @JsonIgnoreProperties(value = { "memberRelations", "childRelations" }, allowSetters = true)
    private Member member;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public MemberReferralStats id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUuid() {
        return this.uuid;
    }

    public MemberReferralStats uuid(String uuid) {
        this.setUuid(uuid);
        return this;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getDirectReferrals() {
        return this.directReferrals;
    }

    public MemberReferralStats directReferrals(Integer directReferrals) {
        this.setDirectReferrals(directReferrals);
        return this;
    }

    public void setDirectReferrals(Integer directReferrals) {
        this.directReferrals = directReferrals;
    }

    public Integer getIndirectReferrals() {
        return this.indirectReferrals;
    }

    public MemberReferralStats indirectReferrals(Integer indirectReferrals) {
        this.setIndirectReferrals(indirectReferrals);
        return this;
    }

    public void setIndirectReferrals(Integer indirectReferrals) {
        this.indirectReferrals = indirectReferrals;
    }

    public Integer getActiveReferrals() {
        return this.activeReferrals;
    }

    public MemberReferralStats activeReferrals(Integer activeReferrals) {
        this.setActiveReferrals(activeReferrals);
        return this;
    }

    public void setActiveReferrals(Integer activeReferrals) {
        this.activeReferrals = activeReferrals;
    }

    public BigDecimal getTotalReward() {
        return this.totalReward;
    }

    public MemberReferralStats totalReward(BigDecimal totalReward) {
        this.setTotalReward(totalReward);
        return this;
    }

    public void setTotalReward(BigDecimal totalReward) {
        this.totalReward = totalReward;
    }

    public BigDecimal getPaidReward() {
        return this.paidReward;
    }

    public MemberReferralStats paidReward(BigDecimal paidReward) {
        this.setPaidReward(paidReward);
        return this;
    }

    public void setPaidReward(BigDecimal paidReward) {
        this.paidReward = paidReward;
    }

    public BigDecimal getPendingReward() {
        return this.pendingReward;
    }

    public MemberReferralStats pendingReward(BigDecimal pendingReward) {
        this.setPendingReward(pendingReward);
        return this;
    }

    public void setPendingReward(BigDecimal pendingReward) {
        this.pendingReward = pendingReward;
    }

    public Integer getMonthlyReferrals() {
        return this.monthlyReferrals;
    }

    public MemberReferralStats monthlyReferrals(Integer monthlyReferrals) {
        this.setMonthlyReferrals(monthlyReferrals);
        return this;
    }

    public void setMonthlyReferrals(Integer monthlyReferrals) {
        this.monthlyReferrals = monthlyReferrals;
    }

    public BigDecimal getMonthlyReward() {
        return this.monthlyReward;
    }

    public MemberReferralStats monthlyReward(BigDecimal monthlyReward) {
        this.setMonthlyReward(monthlyReward);
        return this;
    }

    public void setMonthlyReward(BigDecimal monthlyReward) {
        this.monthlyReward = monthlyReward;
    }

    public Integer getWeeklyReferrals() {
        return this.weeklyReferrals;
    }

    public MemberReferralStats weeklyReferrals(Integer weeklyReferrals) {
        this.setWeeklyReferrals(weeklyReferrals);
        return this;
    }

    public void setWeeklyReferrals(Integer weeklyReferrals) {
        this.weeklyReferrals = weeklyReferrals;
    }

    public Integer getDailyReferrals() {
        return this.dailyReferrals;
    }

    public MemberReferralStats dailyReferrals(Integer dailyReferrals) {
        this.setDailyReferrals(dailyReferrals);
        return this;
    }

    public void setDailyReferrals(Integer dailyReferrals) {
        this.dailyReferrals = dailyReferrals;
    }

    public String getStatMonth() {
        return this.statMonth;
    }

    public MemberReferralStats statMonth(String statMonth) {
        this.setStatMonth(statMonth);
        return this;
    }

    public void setStatMonth(String statMonth) {
        this.statMonth = statMonth;
    }

    public Integer getMaxTeamLevel() {
        return this.maxTeamLevel;
    }

    public MemberReferralStats maxTeamLevel(Integer maxTeamLevel) {
        this.setMaxTeamLevel(maxTeamLevel);
        return this;
    }

    public void setMaxTeamLevel(Integer maxTeamLevel) {
        this.maxTeamLevel = maxTeamLevel;
    }

    public Boolean getDeleted() {
        return this.deleted;
    }

    public MemberReferralStats deleted(Boolean deleted) {
        this.setDeleted(deleted);
        return this;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Instant getCreateTime() {
        return this.createTime;
    }

    public MemberReferralStats createTime(Instant createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public Instant getUpdateTime() {
        return this.updateTime;
    }

    public MemberReferralStats updateTime(Instant updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Instant updateTime) {
        this.updateTime = updateTime;
    }

    public Member getMember() {
        return this.member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public MemberReferralStats member(Member member) {
        this.setMember(member);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MemberReferralStats)) {
            return false;
        }
        return getId() != null && getId().equals(((MemberReferralStats) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "MemberReferralStats{" +
            "id=" + getId() +
            ", uuid='" + getUuid() + "'" +
            ", directReferrals=" + getDirectReferrals() +
            ", indirectReferrals=" + getIndirectReferrals() +
            ", activeReferrals=" + getActiveReferrals() +
            ", totalReward=" + getTotalReward() +
            ", paidReward=" + getPaidReward() +
            ", pendingReward=" + getPendingReward() +
            ", monthlyReferrals=" + getMonthlyReferrals() +
            ", monthlyReward=" + getMonthlyReward() +
            ", weeklyReferrals=" + getWeeklyReferrals() +
            ", dailyReferrals=" + getDailyReferrals() +
            ", statMonth='" + getStatMonth() + "'" +
            ", maxTeamLevel=" + getMaxTeamLevel() +
            ", deleted='" + getDeleted() + "'" +
            ", createTime='" + getCreateTime() + "'" +
            ", updateTime='" + getUpdateTime() + "'" +
            "}";
    }
}
