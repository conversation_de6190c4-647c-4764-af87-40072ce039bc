{"annotations": {"changelogDate": "20250706040203"}, "applications": "*", "documentation": "推荐关系审计日志\\n记录推荐关系的所有变更操作", "dto": "mapstruct", "fields": [{"documentation": "业务唯一标识", "fieldName": "uuid", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"documentation": "关联的推荐关系ID", "fieldName": "relationId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "操作类型：1-创建 2-更新 3-删除 4-冻结 5-解冻", "fieldName": "operationType", "fieldType": "Integer", "fieldValidateRules": ["required", "min", "max"], "fieldValidateRulesMax": "5", "fieldValidateRulesMin": "1"}, {"documentation": "操作前数据（JSON格式）", "fieldName": "beforeData", "fieldType": "TextBlob"}, {"documentation": "操作后数据（JSON格式）", "fieldName": "afterData", "fieldType": "TextBlob"}, {"documentation": "操作原因", "fieldName": "operationReason", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "操作人员ID", "fieldName": "operatorId", "fieldType": "<PERSON>"}, {"documentation": "操作人员姓名", "fieldName": "operatorName", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "操作IP地址", "fieldName": "operatorIp", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "45"}, {"documentation": "操作时间", "fieldName": "operationTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}], "name": "MemberRelationAudit", "pagination": "pagination", "relationships": [{"otherEntityName": "memberRelations", "relationshipName": "relation", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceClass"}