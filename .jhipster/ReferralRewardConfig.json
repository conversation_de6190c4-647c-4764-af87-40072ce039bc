{"annotations": {"changelogDate": "20250706040202"}, "applications": "*", "documentation": "推荐奖励配置实体\\n存储不同层级、不同类型的奖励规则配置", "dto": "mapstruct", "fields": [{"documentation": "业务唯一标识", "fieldName": "uuid", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"documentation": "配置名称", "fieldName": "config<PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "100"}, {"documentation": "会员等级（0表示适用于所有等级）", "fieldName": "memberLevel", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "10", "fieldValidateRulesMin": "0"}, {"documentation": "推荐层级（1-直推，2-二级，等）", "fieldName": "referralLevel", "fieldType": "Integer", "fieldValidateRules": ["required", "min", "max"], "fieldValidateRulesMax": "10", "fieldValidateRulesMin": "1"}, {"documentation": "奖励类型：1-固定金额 2-比例 3-阶梯式", "fieldName": "rewardType", "fieldType": "Integer", "fieldValidateRules": ["required", "min", "max"], "fieldValidateRulesMax": "3", "fieldValidateRulesMin": "1"}, {"documentation": "奖励金额/比例（单位：分或千分比）", "fieldName": "rewardValue", "fieldType": "BigDecimal", "fieldValidateRules": ["required", "min"], "fieldValidateRulesMin": "0"}, {"documentation": "最小奖励金额（单位：分）", "fieldName": "minReward", "fieldType": "BigDecimal", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "最大奖励金额（单位：分）", "fieldName": "max<PERSON><PERSON><PERSON>", "fieldType": "BigDecimal", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "配置状态：0-禁用 1-启用", "fieldName": "status", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "1", "fieldValidateRulesMin": "0"}, {"documentation": "生效时间", "fieldName": "effectiveTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "失效时间", "fieldName": "expireTime", "fieldType": "Instant"}, {"documentation": "配置说明", "fieldName": "description", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "逻辑删除标记", "fieldName": "deleted", "fieldType": "Boolean"}, {"documentation": "记录创建时间", "fieldName": "createTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "记录更新时间", "fieldName": "updateTime", "fieldType": "Instant"}], "name": "ReferralRewardConfig", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceClass"}