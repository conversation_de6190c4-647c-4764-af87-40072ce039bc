{"annotations": {"changelogDate": "20250706040200"}, "applications": "*", "documentation": "会员关系实体 - 会员层级关系表\\n存储会员推荐关系、团队结构等层级信息", "dto": "mapstruct", "fields": [{"documentation": "业务唯一标识（32位UUID，用于对外接口）", "fieldName": "uuid", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"documentation": "会员ID（当前会员）", "fieldName": "memberId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "上级会员ID（推荐人/上级）", "fieldName": "parentId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "推荐码（用于记录通过哪个推荐码建立的关系）", "fieldName": "referralCode", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"documentation": "关系类型：1-推荐人 2-团队长 3-代理商 4-合作伙伴", "fieldName": "relationType", "fieldType": "Integer", "fieldValidateRules": ["required", "min", "max"], "fieldValidateRulesMax": "4", "fieldValidateRulesMin": "1"}, {"documentation": "层级深度（1-直接推荐，2-二级推荐，以此类推）", "fieldName": "level", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "10", "fieldValidateRulesMin": "1"}, {"documentation": "完整路径（用/分隔的ID链，便于快速查询）", "fieldName": "relationPath", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"documentation": "关系状态：0-失效 1-有效 2-暂停 3-冻结", "fieldName": "status", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "3", "fieldValidateRulesMin": "0"}, {"documentation": "推荐奖励金额（单位：分）", "fieldName": "rewardAmount", "fieldType": "BigDecimal", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "奖励状态：0-未发放 1-已发放 2-已取消 3-计算中", "fieldName": "rewardStatus", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "3", "fieldValidateRulesMin": "0"}, {"documentation": "奖励发放时间", "fieldName": "rewardTime", "fieldType": "Instant"}, {"documentation": "关系生效时间", "fieldName": "effectiveTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "关系失效时间", "fieldName": "expireTime", "fieldType": "Instant"}, {"documentation": "冻结原因", "fieldName": "frozenReason", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "255"}, {"documentation": "冻结时间", "fieldName": "frozenTime", "fieldType": "Instant"}, {"documentation": "解冻时间", "fieldName": "unfrozenTime", "fieldType": "Instant"}, {"documentation": "备注说明", "fieldName": "remark", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "数据来源：1-系统自动 2-手动录入 3-批量导入", "fieldName": "dataSource", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "3", "fieldValidateRulesMin": "1"}, {"documentation": "操作人员ID", "fieldName": "operatorId", "fieldType": "<PERSON>"}, {"documentation": "逻辑删除标记：false-正常 true-已删除", "fieldName": "deleted", "fieldType": "Boolean"}, {"documentation": "记录创建时间", "fieldName": "createTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "记录更新时间", "fieldName": "updateTime", "fieldType": "Instant"}], "name": "MemberRelations", "pagination": "pagination", "relationships": [{"otherEntityName": "member", "otherEntityRelationshipName": "memberRelations", "relationshipName": "member", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "member", "otherEntityRelationshipName": "childRelations", "relationshipName": "parent", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceClass"}