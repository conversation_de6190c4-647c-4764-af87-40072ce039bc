{"annotations": {"changelogDate": "20250706040204"}, "applications": "*", "documentation": "推荐码管理实体\\n管理推荐码的生成、使用、过期等", "dto": "mapstruct", "fields": [{"documentation": "业务唯一标识", "fieldName": "uuid", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"documentation": "推荐码", "fieldName": "referralCode", "fieldType": "String", "fieldValidateRules": ["required", "unique", "maxlength"], "fieldValidateRulesMaxlength": "10"}, {"documentation": "所属会员ID", "fieldName": "memberId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "推荐码类型：1-个人专属 2-临时活动 3-批量生成", "fieldName": "codeType", "fieldType": "Integer", "fieldValidateRules": ["required", "min", "max"], "fieldValidateRulesMax": "3", "fieldValidateRulesMin": "1"}, {"documentation": "使用次数限制（0表示无限制）", "fieldName": "usageLimit", "fieldType": "Integer", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "已使用次数", "fieldName": "usedCount", "fieldType": "Integer", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "推荐码状态：0-禁用 1-启用 2-过期", "fieldName": "status", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "2", "fieldValidateRulesMin": "0"}, {"documentation": "生效时间", "fieldName": "effectiveTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "过期时间", "fieldName": "expireTime", "fieldType": "Instant"}, {"documentation": "推荐码说明", "fieldName": "description", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "255"}, {"documentation": "逻辑删除标记", "fieldName": "deleted", "fieldType": "Boolean"}, {"documentation": "记录创建时间", "fieldName": "createTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "记录更新时间", "fieldName": "updateTime", "fieldType": "Instant"}], "name": "ReferralCodeManagement", "pagination": "pagination", "relationships": [{"otherEntityName": "member", "relationshipName": "member", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceClass"}