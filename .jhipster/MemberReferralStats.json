{"annotations": {"changelogDate": "20250706040201"}, "applications": "*", "documentation": "会员推荐统计实体 - 推荐业绩统计\\n存储会员推荐数据的统计信息，便于快速查询", "dto": "mapstruct", "fields": [{"documentation": "业务唯一标识（32位UUID）", "fieldName": "uuid", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"documentation": "会员ID", "fieldName": "memberId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "直接推荐人数", "fieldName": "directReferrals", "fieldType": "Integer", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "间接推荐人数（团队总人数）", "fieldName": "indirectReferrals", "fieldType": "Integer", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "有效推荐人数（状态为有效的推荐）", "fieldName": "activeReferrals", "fieldType": "Integer", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "总推荐奖励金额（单位：分）", "fieldName": "totalReward", "fieldType": "BigDecimal", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "已发放奖励金额", "fieldName": "paidReward", "fieldType": "BigDecimal", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "待发放奖励金额", "fieldName": "pendingReward", "fieldType": "BigDecimal", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "本月推荐人数", "fieldName": "monthlyReferrals", "fieldType": "Integer", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "本月推荐奖励（单位：分）", "fieldName": "monthlyReward", "fieldType": "BigDecimal", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "本周推荐人数", "fieldName": "weeklyReferrals", "fieldType": "Integer", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "今日推荐人数", "fieldName": "dailyReferrals", "fieldType": "Integer", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "统计月份（格式：YYYY-MM）", "fieldName": "statMonth", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "7"}, {"documentation": "团队层级深度", "fieldName": "maxTeamLevel", "fieldType": "Integer", "fieldValidateRules": ["min"], "fieldValidateRulesMin": "0"}, {"documentation": "逻辑删除标记：false-正常 true-已删除", "fieldName": "deleted", "fieldType": "Boolean"}, {"documentation": "记录创建时间", "fieldName": "createTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "记录更新时间", "fieldName": "updateTime", "fieldType": "Instant"}], "name": "MemberReferralStats", "pagination": "pagination", "relationships": [{"otherEntityName": "member", "relationshipName": "member", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceClass"}