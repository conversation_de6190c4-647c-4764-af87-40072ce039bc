/**
 * 会员实体 - 会员信息表
 * 存储用户基本信息、微信信息、登录状态等
 */
entity Member {
  /** 业务唯一标识（32位UUID，用于对外接口） */
  uuid String required unique
  /** 微信小程序openid（用户在当前小程序的唯一标识） */
  openid String maxlength(64)
  /** 微信unionid（用户在微信开放平台的唯一标识） */
  unionid String maxlength(64)
  /** 用户昵称（显示名称） */
  nickname String maxlength(64)
  /** 用户头像URL（存储图片地址） */
  avatar String maxlength(255)
  /** 性别枚举：0-未知 1-男性 2-女性 */
  gender Integer min(0) max(2)
  /** 手机号码（用于登录和联系） */
  phone String maxlength(20) pattern(/^1[3-9]\d{9}$/)
  /** 邮箱地址（用于登录和通知） */
  email String maxlength(64) pattern(/^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/)
  /** 登录密码（BCrypt加密存储） */
  password String maxlength(128)
  /** 真实姓名（实名认证使用） */
  realName String maxlength(32)
  /** 会员等级：0-普通会员 1-VIP会员 2-企业会员 */
  level Integer min(0) max(2)
  /** 账户状态：0-禁用 1-正常 */
  status Integer min(0) max(1)
  /** 最后登录时间 */
  lastLoginTime Instant
  /** 最后登录IP地址 */
  lastLoginIp String maxlength(64)
  /** 逻辑删除标记：false-正常 true-已删除 */
  deleted Boolean
  /** 记录创建时间 */
  createTime Instant
  /** 记录更新时间 */
  updateTime Instant
}

/**
 * 会员关系实体 - 会员层级关系表
 * 存储会员推荐关系、团队结构等层级信息
 */
entity MemberRelations {
  /** 业务唯一标识（32位UUID，用于对外接口） */
  uuid String required unique
  /** 会员ID（当前会员） */
  memberId Long required
  /** 上级会员ID（推荐人/上级） */
  parentId Long required
  /** 推荐码（用于记录通过哪个推荐码建立的关系） */
  referralCode String maxlength(10)
  /** 关系类型：1-推荐人 2-团队长 3-代理商 4-合作伙伴 */
  relationType Integer required min(1) max(4)
  /** 层级深度（1-直接推荐，2-二级推荐，以此类推） */
  level Integer min(1) max(10)
  /** 完整路径（用/分隔的ID链，便于快速查询） */
  relationPath String maxlength(1000)
  /** 关系状态：0-失效 1-有效 2-暂停 3-冻结 */
  status Integer min(0) max(3)
  /** 推荐奖励金额（单位：分） */
  rewardAmount BigDecimal min(0)
  /** 奖励状态：0-未发放 1-已发放 2-已取消 3-计算中 */
  rewardStatus Integer min(0) max(3)
  /** 奖励发放时间 */
  rewardTime Instant
  /** 关系生效时间 */
  effectiveTime Instant required
  /** 关系失效时间 */
  expireTime Instant
  /** 冻结原因 */
  frozenReason String maxlength(255)
  /** 冻结时间 */
  frozenTime Instant
  /** 解冻时间 */
  unfrozenTime Instant
  /** 备注说明 */
  remark String maxlength(500)
  /** 数据来源：1-系统自动 2-手动录入 3-批量导入 */
  dataSource Integer min(1) max(3)
  /** 操作人员ID */
  operatorId Long
  /** 逻辑删除标记：false-正常 true-已删除 */
  deleted Boolean
  /** 记录创建时间 */
  createTime Instant required
  /** 记录更新时间 */
  updateTime Instant
}

/**
 * 会员推荐统计实体 - 推荐业绩统计
 * 存储会员推荐数据的统计信息，便于快速查询
 */
entity MemberReferralStats {
  /** 业务唯一标识（32位UUID） */
  uuid String required unique
  /** 会员ID */
  memberId Long required
  /** 直接推荐人数 */
  directReferrals Integer min(0)
  /** 间接推荐人数（团队总人数） */
  indirectReferrals Integer min(0)
  /** 有效推荐人数（状态为有效的推荐） */
  activeReferrals Integer min(0)
  /** 总推荐奖励金额（单位：分） */
  totalReward BigDecimal min(0)
  /** 已发放奖励金额 */
  paidReward BigDecimal min(0)
  /** 待发放奖励金额 */
  pendingReward BigDecimal min(0)
  /** 本月推荐人数 */
  monthlyReferrals Integer min(0)
  /** 本月推荐奖励（单位：分） */
  monthlyReward BigDecimal min(0)
  /** 本周推荐人数 */
  weeklyReferrals Integer min(0)
  /** 今日推荐人数 */
  dailyReferrals Integer min(0)
  /** 统计月份（格式：YYYY-MM） */
  statMonth String required maxlength(7)
  /** 团队层级深度 */
  maxTeamLevel Integer min(0)
  /** 逻辑删除标记：false-正常 true-已删除 */
  deleted Boolean
  /** 记录创建时间 */
  createTime Instant required
  /** 记录更新时间 */
  updateTime Instant
}

/**
 * 推荐奖励配置实体
 * 存储不同层级、不同类型的奖励规则配置
 */
entity ReferralRewardConfig {
  /** 业务唯一标识 */
  uuid String required unique
  /** 配置名称 */
  configName String required maxlength(100)
  /** 会员等级（0表示适用于所有等级） */
  memberLevel Integer min(0) max(10)
  /** 推荐层级（1-直推，2-二级，等） */
  referralLevel Integer required min(1) max(10)
  /** 奖励类型：1-固定金额 2-比例 3-阶梯式 */
  rewardType Integer required min(1) max(3)
  /** 奖励金额/比例（单位：分或千分比） */
  rewardValue BigDecimal required min(0)
  /** 最小奖励金额（单位：分） */
  minReward BigDecimal min(0)
  /** 最大奖励金额（单位：分） */
  maxReward BigDecimal min(0)
  /** 配置状态：0-禁用 1-启用 */
  status Integer min(0) max(1)
  /** 生效时间 */
  effectiveTime Instant required
  /** 失效时间 */
  expireTime Instant
  /** 配置说明 */
  description String maxlength(500)
  /** 逻辑删除标记 */
  deleted Boolean
  /** 记录创建时间 */
  createTime Instant required
  /** 记录更新时间 */
  updateTime Instant
}

/**
 * 推荐关系审计日志
 * 记录推荐关系的所有变更操作
 */
entity MemberRelationAudit {
  /** 业务唯一标识 */
  uuid String required unique
  /** 关联的推荐关系ID */
  relationId Long required
  /** 操作类型：1-创建 2-更新 3-删除 4-冻结 5-解冻 */
  operationType Integer required min(1) max(5)
  /** 操作前数据（JSON格式） */
  beforeData TextBlob
  /** 操作后数据（JSON格式） */
  afterData TextBlob
  /** 操作原因 */
  operationReason String maxlength(500)
  /** 操作人员ID */
  operatorId Long
  /** 操作人员姓名 */
  operatorName String maxlength(50)
  /** 操作IP地址 */
  operatorIp String maxlength(45)
  /** 操作时间 */
  operationTime Instant required
}

/**
 * 推荐码管理实体
 * 管理推荐码的生成、使用、过期等
 */
entity ReferralCodeManagement {
  /** 业务唯一标识 */
  uuid String required unique
  /** 推荐码 */
  referralCode String required unique maxlength(10)
  /** 所属会员ID */
  memberId Long required
  /** 推荐码类型：1-个人专属 2-临时活动 3-批量生成 */
  codeType Integer required min(1) max(3)
  /** 使用次数限制（0表示无限制） */
  usageLimit Integer min(0)
  /** 已使用次数 */
  usedCount Integer min(0)
  /** 推荐码状态：0-禁用 1-启用 2-过期 */
  status Integer min(0) max(2)
  /** 生效时间 */
  effectiveTime Instant required
  /** 过期时间 */
  expireTime Instant
  /** 推荐码说明 */
  description String maxlength(255)
  /** 逻辑删除标记 */
  deleted Boolean
  /** 记录创建时间 */
  createTime Instant required
  /** 记录更新时间 */
  updateTime Instant
}

/* =============================  关联关系  ============================= */

/**
 * 多对一关系定义
 * 定义实体间的外键关联关系，确保数据完整性和业务逻辑正确性
 */
relationship ManyToOne {
  /* ========== 会员推荐关系 ========== */
  // 推荐关系关联会员 - 当前会员
  MemberRelations{member} to Member{memberRelations}
  // 推荐关系关联会员 - 上级会员（推荐人）
  MemberRelations{parent} to Member{childRelations}

  // 推荐统计关联会员
  MemberReferralStats{member} to Member

  // 推荐关系审计日志
  MemberRelationAudit{relation} to MemberRelations

  // 推荐码管理关联会员
  ReferralCodeManagement{member} to Member
}


/* =======================  JHipster生成策略配置  ======================= */

/**
 * DTO生成策略
 * 使用MapStruct自动生成实体与DTO之间的映射代码
 * 提供类型安全的对象转换，减少手动编码错误
 */
dto all with mapstruct

/**
 * 服务层生成策略
 * 为所有实体生成Service类，提供业务逻辑处理层
 * 遵循分层架构原则，分离控制器和数据访问逻辑
 */
service all with serviceClass

/**
 * 分页策略
 * 为所有实体启用分页功能，支持大数据量查询
 * 提供标准的分页参数：page、size、sort等
 */
paginate all with pagination
